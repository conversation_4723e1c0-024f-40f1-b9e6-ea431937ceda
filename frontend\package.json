{"name": "us-insurance-details-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.6.0", "@supabase/supabase-js": "^2.21.0", "autoprefixer": "^10.4.14", "axios": "^1.4.0", "next": "13.4.7", "postcss": "^8.4.24", "react": "18.2.0", "react-dom": "18.2.0", "recharts": "^3.0.2", "tailwindcss": "^3.3.2", "typescript": "5.1.3"}, "devDependencies": {"@types/node": "20.3.1", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "eslint": "8.43.0", "eslint-config-next": "13.4.7"}}