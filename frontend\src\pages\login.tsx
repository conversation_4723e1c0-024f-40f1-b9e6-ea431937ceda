import React from 'react';
import Head from 'next/head';
import { LoginForm } from '../components/auth/LoginForm';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';

const LoginPage: React.FC = () => {
  return (
    <ProtectedRoute requireAuth={false}>
      <Head>
        <title>Sign In - US Insurance Platform</title>
        <meta name="description" content="Sign in to your US Insurance Platform account" />
      </Head>
      <LoginForm />
    </ProtectedRoute>
  );
};

export default LoginPage;
