import axios, { AxiosResponse } from 'axios';
import { User, RegisterData } from '../contexts/AuthContext';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

// Types for API responses
interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  user: User;
}

interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(TOKEN_KEY);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/api/auth/refresh-token`, {
            token: refreshToken,
          });

          const { access_token, refresh_token } = response.data;
          localStorage.setItem(TOKEN_KEY, access_token);
          localStorage.setItem(REFRESH_TOKEN_KEY, refresh_token);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        authService.logout();
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

export const authService = {
  // Login user
  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      // Create form data for OAuth2 compatibility
      const formData = new FormData();
      formData.append('username', email);
      formData.append('password', password);

      const response: AxiosResponse<LoginResponse> = await apiClient.post(
        '/api/auth/login',
        formData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      const { access_token, refresh_token, user } = response.data;

      // Store tokens securely
      localStorage.setItem(TOKEN_KEY, access_token);
      localStorage.setItem(REFRESH_TOKEN_KEY, refresh_token);

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || 'Login failed. Please check your credentials.'
      );
    }
  },

  // Register new user
  async register(userData: RegisterData): Promise<User> {
    try {
      const response: AxiosResponse<User> = await apiClient.post(
        '/api/auth/register',
        userData
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || 'Registration failed. Please try again.'
      );
    }
  },

  // Get current user (for auth check)
  async getCurrentUser(): Promise<User> {
    try {
      const response: AxiosResponse<User> = await apiClient.get('/api/users/me');
      return response.data;
    } catch (error: any) {
      throw new Error('Failed to get user information');
    }
  },

  // Logout user
  logout(): void {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
  },

  // Get stored token
  getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY);
  },

  // Remove stored token
  removeToken(): void {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
  },

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.getToken();
  },

  // Refresh access token
  async refreshToken(): Promise<TokenResponse> {
    try {
      const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response: AxiosResponse<TokenResponse> = await apiClient.post(
        '/api/auth/refresh-token',
        { token: refreshToken }
      );

      const { access_token, refresh_token } = response.data;
      localStorage.setItem(TOKEN_KEY, access_token);
      localStorage.setItem(REFRESH_TOKEN_KEY, refresh_token);

      return response.data;
    } catch (error: any) {
      this.logout();
      throw new Error('Token refresh failed');
    }
  },
};

export default authService;
