'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

function mapKeys(object, getNewKey) {
    const result = {};
    const keys = Object.keys(object);
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const value = object[key];
        result[getNew<PERSON>ey(value, key, object)] = value;
    }
    return result;
}

exports.mapKeys = mapKeys;
