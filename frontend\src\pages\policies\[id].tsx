import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { ProtectedRoute } from '../../components/auth/ProtectedRoute';
import { policyApi, carrierApi, documentApi } from '../../services/apiService';
import { InsurancePolicy, InsuranceCarrier, CoverageBenefit, RedFlag, PolicyDocument } from '../../types/api';

export default function PolicyDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  
  const [policy, setPolicy] = useState<InsurancePolicy | null>(null);
  const [carrier, setCarrier] = useState<InsuranceCarrier | null>(null);
  const [document, setDocument] = useState<PolicyDocument | null>(null);
  const [benefits, setBenefits] = useState<CoverageBenefit[]>([]);
  const [redFlags, setRedFlags] = useState<RedFlag[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  useEffect(() => {
    if (id && typeof id === 'string') {
      loadPolicyData(id);
    }
  }, [id]);

  const loadPolicyData = async (policyId: string) => {
    try {
      setLoading(true);
      
      // Load policy details
      const policyData = await policyApi.getPolicy(policyId);
      setPolicy(policyData);

      // Load related data in parallel
      const [benefitsData, redFlagsData, documentData, carriersData] = await Promise.all([
        policyApi.getPolicyBenefits(policyId).catch(() => []),
        policyApi.getRedFlags(policyId).catch(() => []),
        documentApi.getDocument(policyData.document_id).catch(() => null),
        carrierApi.getCarriers().catch(() => [])
      ]);

      setBenefits(benefitsData);
      setRedFlags(redFlagsData);
      setDocument(documentData);
      
      // Find the carrier
      if (policyData.carrier_id) {
        const carrierData = carriersData.find(c => c.id === policyData.carrier_id);
        setCarrier(carrierData || null);
      }

      setError(null);
    } catch (err) {
      console.error('Error loading policy:', err);
      setError('Failed to load policy details');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!policy || !confirm('Are you sure you want to delete this policy? This action cannot be undone.')) {
      return;
    }

    try {
      setDeleteLoading(true);
      await policyApi.deletePolicy(policy.id);
      router.push('/policies');
    } catch (err) {
      console.error('Error deleting policy:', err);
      setError('Failed to delete policy');
    } finally {
      setDeleteLoading(false);
    }
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getPolicyTypeColor = (type?: string) => {
    switch (type) {
      case 'health': return 'bg-blue-100 text-blue-800';
      case 'dental': return 'bg-green-100 text-green-800';
      case 'vision': return 'bg-purple-100 text-purple-800';
      case 'life': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity?: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getBenefitCategoryColor = (category?: string) => {
    switch (category) {
      case 'preventive': return 'bg-green-100 text-green-800';
      case 'emergency': return 'bg-red-100 text-red-800';
      case 'specialist': return 'bg-purple-100 text-purple-800';
      case 'prescription': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error || !policy) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Policy Not Found</h2>
            <p className="text-gray-600 mb-4">{error || 'The requested policy could not be found.'}</p>
            <Link
              href="/policies"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Back to Policies
            </Link>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div>
                <div className="flex items-center space-x-2 text-sm text-gray-500 mb-1">
                  <Link href="/policies" className="hover:text-gray-700">Policies</Link>
                  <span>/</span>
                  <span>{policy.policy_name}</span>
                </div>
                <h1 className="text-3xl font-bold text-gray-900">{policy.policy_name}</h1>
                <div className="flex items-center space-x-4 mt-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPolicyTypeColor(policy.policy_type)}`}>
                    {policy.policy_type || 'Unknown'}
                  </span>
                  {policy.policy_number && (
                    <span className="text-sm text-gray-600">#{policy.policy_number}</span>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Link
                  href={`/policies/${policy.id}/edit`}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Edit Policy
                </Link>
                <button
                  onClick={handleDelete}
                  disabled={deleteLoading}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
                >
                  {deleteLoading ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Policy Information */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Policy Information</h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Policy Name</label>
                      <p className="mt-1 text-sm text-gray-900">{policy.policy_name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Policy Number</label>
                      <p className="mt-1 text-sm text-gray-900">{policy.policy_number || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Plan Year</label>
                      <p className="mt-1 text-sm text-gray-900">{policy.plan_year || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Group Number</label>
                      <p className="mt-1 text-sm text-gray-900">{policy.group_number || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Network Type</label>
                      <p className="mt-1 text-sm text-gray-900">{policy.network_type || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Carrier</label>
                      <p className="mt-1 text-sm text-gray-900">{carrier?.name || 'Unknown'}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Coverage Details */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Coverage Details</h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Individual Deductible</label>
                      <p className="mt-1 text-sm text-gray-900">{formatCurrency(policy.deductible_individual)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Family Deductible</label>
                      <p className="mt-1 text-sm text-gray-900">{formatCurrency(policy.deductible_family)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Individual Out-of-Pocket Max</label>
                      <p className="mt-1 text-sm text-gray-900">{formatCurrency(policy.out_of_pocket_max_individual)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Family Out-of-Pocket Max</label>
                      <p className="mt-1 text-sm text-gray-900">{formatCurrency(policy.out_of_pocket_max_family)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Monthly Premium</label>
                      <p className="mt-1 text-sm text-gray-900">{formatCurrency(policy.premium_monthly)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Annual Premium</label>
                      <p className="mt-1 text-sm text-gray-900">{formatCurrency(policy.premium_annual)}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Benefits */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Coverage Benefits ({benefits.length})</h3>
                </div>
                <div className="p-6">
                  {benefits.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">No benefits found for this policy.</p>
                  ) : (
                    <div className="space-y-4">
                      {benefits.map((benefit) => (
                        <div key={benefit.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-sm font-medium text-gray-900">{benefit.benefit_name}</h4>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getBenefitCategoryColor(benefit.benefit_category)}`}>
                              {benefit.benefit_category}
                            </span>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            {benefit.coverage_percentage && (
                              <div>
                                <span className="text-gray-500">Coverage:</span>
                                <span className="ml-1 text-gray-900">{benefit.coverage_percentage}%</span>
                              </div>
                            )}
                            {benefit.copay_amount && (
                              <div>
                                <span className="text-gray-500">Copay:</span>
                                <span className="ml-1 text-gray-900">{formatCurrency(benefit.copay_amount)}</span>
                              </div>
                            )}
                            {benefit.requires_preauth && (
                              <div>
                                <span className="text-red-600">Pre-auth Required</span>
                              </div>
                            )}
                            {benefit.network_restriction && (
                              <div>
                                <span className="text-gray-500">Network:</span>
                                <span className="ml-1 text-gray-900">{benefit.network_restriction}</span>
                              </div>
                            )}
                          </div>
                          {benefit.notes && (
                            <p className="mt-2 text-sm text-gray-600">{benefit.notes}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Policy Dates */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Important Dates</h3>
                </div>
                <div className="p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Effective Date</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(policy.effective_date)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Expiration Date</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(policy.expiration_date)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Created</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(policy.created_at)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Last Updated</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(policy.updated_at)}</p>
                  </div>
                </div>
              </div>

              {/* Red Flags */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Red Flags ({redFlags.length})</h3>
                </div>
                <div className="p-6">
                  {redFlags.length === 0 ? (
                    <div className="text-center py-4">
                      <div className="text-green-500 text-2xl mb-2">✅</div>
                      <p className="text-sm text-gray-500">No red flags detected</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {redFlags.map((flag) => (
                        <div key={flag.id} className={`border rounded-lg p-3 ${getSeverityColor(flag.severity)}`}>
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-sm font-medium">{flag.title}</h4>
                            <span className="text-xs font-medium uppercase">{flag.severity}</span>
                          </div>
                          <p className="text-sm">{flag.description}</p>
                          {flag.recommendation && (
                            <p className="text-xs mt-2 font-medium">
                              Recommendation: {flag.recommendation}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Document Info */}
              {document && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">Source Document</h3>
                  </div>
                  <div className="p-6">
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Filename</label>
                        <p className="mt-1 text-sm text-gray-900">{document.original_filename}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Processing Status</label>
                        <p className="mt-1 text-sm text-gray-900 capitalize">{document.processing_status}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Upload Method</label>
                        <p className="mt-1 text-sm text-gray-900 capitalize">{document.upload_method.replace('_', ' ')}</p>
                      </div>
                      <Link
                        href={`/documents/${document.id}`}
                        className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                      >
                        View Document →
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
