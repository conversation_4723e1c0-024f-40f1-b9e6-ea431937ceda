'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const isArrayBuffer = require('./isArrayBuffer.js');
const isBlob = require('./isBlob.js');
const isBoolean = require('./isBoolean.js');
const isBrowser = require('./isBrowser.js');
const isBuffer = require('./isBuffer.js');
const isDate = require('./isDate.js');
const isEqual = require('./isEqual.js');
const isEqualWith = require('./isEqualWith.js');
const isError = require('./isError.js');
const isFile = require('./isFile.js');
const isFunction = require('./isFunction.js');
const isJSON = require('./isJSON.js');
const isJSONValue = require('./isJSONValue.js');
const isLength = require('./isLength.js');
const isMap = require('./isMap.js');
const isNil = require('./isNil.js');
const isNode = require('./isNode.js');
const isNotNil = require('./isNotNil.js');
const isNull = require('./isNull.js');
const isPlainObject = require('./isPlainObject.js');
const isPrimitive = require('./isPrimitive.js');
const isPromise = require('./isPromise.js');
const isRegExp = require('./isRegExp.js');
const isSet = require('./isSet.js');
const isString = require('./isString.js');
const isSymbol = require('./isSymbol.js');
const isTypedArray = require('./isTypedArray.js');
const isUndefined = require('./isUndefined.js');
const isWeakMap = require('./isWeakMap.js');
const isWeakSet = require('./isWeakSet.js');



exports.isArrayBuffer = isArrayBuffer.isArrayBuffer;
exports.isBlob = isBlob.isBlob;
exports.isBoolean = isBoolean.isBoolean;
exports.isBrowser = isBrowser.isBrowser;
exports.isBuffer = isBuffer.isBuffer;
exports.isDate = isDate.isDate;
exports.isEqual = isEqual.isEqual;
exports.isEqualWith = isEqualWith.isEqualWith;
exports.isError = isError.isError;
exports.isFile = isFile.isFile;
exports.isFunction = isFunction.isFunction;
exports.isJSON = isJSON.isJSON;
exports.isJSONArray = isJSONValue.isJSONArray;
exports.isJSONObject = isJSONValue.isJSONObject;
exports.isJSONValue = isJSONValue.isJSONValue;
exports.isLength = isLength.isLength;
exports.isMap = isMap.isMap;
exports.isNil = isNil.isNil;
exports.isNode = isNode.isNode;
exports.isNotNil = isNotNil.isNotNil;
exports.isNull = isNull.isNull;
exports.isPlainObject = isPlainObject.isPlainObject;
exports.isPrimitive = isPrimitive.isPrimitive;
exports.isPromise = isPromise.isPromise;
exports.isRegExp = isRegExp.isRegExp;
exports.isSet = isSet.isSet;
exports.isString = isString.isString;
exports.isSymbol = isSymbol.isSymbol;
exports.isTypedArray = isTypedArray.isTypedArray;
exports.isUndefined = isUndefined.isUndefined;
exports.isWeakMap = isWeakMap.isWeakMap;
exports.isWeakSet = isWeakSet.isWeakSet;
