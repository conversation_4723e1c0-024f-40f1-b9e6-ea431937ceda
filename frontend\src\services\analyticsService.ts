import { policyApi, documentApi, carrierApi } from './apiService';
import { InsurancePolicy, PolicyDocument, InsuranceCarrier } from '../types/api';

export interface PolicyDistribution {
  type: string;
  count: number;
  percentage: number;
  color: string;
}

export interface CarrierDistribution {
  carrier_name: string;
  carrier_code: string;
  policy_count: number;
  document_count: number;
  percentage: number;
  color: string;
}

export interface PremiumAnalytics {
  month: string;
  total_premium: number;
  average_premium: number;
  policy_count: number;
}

export interface DocumentProcessingStats {
  status: string;
  count: number;
  percentage: number;
  color: string;
}

export interface TrendData {
  date: string;
  policies: number;
  documents: number;
  carriers: number;
}

export interface CarrierPerformance {
  carrier_id: string;
  carrier_name: string;
  carrier_code: string;
  total_policies: number;
  total_documents: number;
  avg_processing_time: number;
  success_rate: number;
  recent_activity: number;
}

export interface AnalyticsSummary {
  total_policies: number;
  total_documents: number;
  total_carriers: number;
  active_carriers: number;
  processing_documents: number;
  failed_documents: number;
  avg_premium: number;
  total_premium: number;
}

class AnalyticsService {
  private readonly POLICY_TYPE_COLORS = {
    'health': '#3B82F6',
    'dental': '#10B981',
    'vision': '#8B5CF6',
    'life': '#F59E0B',
    'disability': '#EF4444',
    'other': '#6B7280'
  };

  private readonly PROCESSING_STATUS_COLORS = {
    'completed': '#10B981',
    'processing': '#F59E0B',
    'failed': '#EF4444',
    'pending': '#6B7280'
  };

  private readonly CARRIER_COLORS = [
    '#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444',
    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
  ];

  async getAnalyticsSummary(): Promise<AnalyticsSummary> {
    try {
      const [policies, documents, carriers] = await Promise.all([
        policyApi.getPolicies(),
        documentApi.getDocuments(),
        carrierApi.getCarriers()
      ]);

      const activeCarriers = carriers.filter(c => c.is_active).length;
      const processingDocuments = documents.filter(d => d.processing_status === 'processing').length;
      const failedDocuments = documents.filter(d => d.processing_status === 'failed').length;
      
      const totalPremium = policies.reduce((sum, p) => sum + (p.premium_amount || 0), 0);
      const avgPremium = policies.length > 0 ? totalPremium / policies.length : 0;

      return {
        total_policies: policies.length,
        total_documents: documents.length,
        total_carriers: carriers.length,
        active_carriers: activeCarriers,
        processing_documents: processingDocuments,
        failed_documents: failedDocuments,
        avg_premium: avgPremium,
        total_premium: totalPremium
      };
    } catch (error) {
      console.error('Error fetching analytics summary:', error);
      throw error;
    }
  }

  async getPolicyDistribution(): Promise<PolicyDistribution[]> {
    try {
      const policies = await policyApi.getPolicies();
      const distribution = new Map<string, number>();

      policies.forEach(policy => {
        const type = policy.policy_type || 'other';
        distribution.set(type, (distribution.get(type) || 0) + 1);
      });

      const total = policies.length;
      return Array.from(distribution.entries()).map(([type, count]) => ({
        type: type.charAt(0).toUpperCase() + type.slice(1),
        count,
        percentage: total > 0 ? Math.round((count / total) * 100) : 0,
        color: this.POLICY_TYPE_COLORS[type as keyof typeof this.POLICY_TYPE_COLORS] || this.POLICY_TYPE_COLORS.other
      }));
    } catch (error) {
      console.error('Error fetching policy distribution:', error);
      throw error;
    }
  }

  async getCarrierDistribution(): Promise<CarrierDistribution[]> {
    try {
      const [policies, documents, carriers] = await Promise.all([
        policyApi.getPolicies(),
        documentApi.getDocuments(),
        carrierApi.getCarriers()
      ]);

      const carrierMap = new Map(carriers.map(c => [c.id, c]));
      const carrierStats = new Map<string, { policies: number; documents: number }>();

      // Count policies by carrier
      policies.forEach(policy => {
        if (policy.carrier_id) {
          const stats = carrierStats.get(policy.carrier_id) || { policies: 0, documents: 0 };
          stats.policies += 1;
          carrierStats.set(policy.carrier_id, stats);
        }
      });

      // Count documents by carrier
      documents.forEach(document => {
        if (document.carrier_id) {
          const stats = carrierStats.get(document.carrier_id) || { policies: 0, documents: 0 };
          stats.documents += 1;
          carrierStats.set(document.carrier_id, stats);
        }
      });

      const totalPolicies = policies.length;
      return Array.from(carrierStats.entries())
        .map(([carrierId, stats], index) => {
          const carrier = carrierMap.get(carrierId);
          return {
            carrier_name: carrier?.name || 'Unknown',
            carrier_code: carrier?.code || 'UNK',
            policy_count: stats.policies,
            document_count: stats.documents,
            percentage: totalPolicies > 0 ? Math.round((stats.policies / totalPolicies) * 100) : 0,
            color: this.CARRIER_COLORS[index % this.CARRIER_COLORS.length]
          };
        })
        .sort((a, b) => b.policy_count - a.policy_count);
    } catch (error) {
      console.error('Error fetching carrier distribution:', error);
      throw error;
    }
  }

  async getPremiumAnalytics(): Promise<PremiumAnalytics[]> {
    try {
      const policies = await policyApi.getPolicies();
      const monthlyData = new Map<string, { total: number; count: number }>();

      policies.forEach(policy => {
        if (policy.effective_date && policy.premium_amount) {
          const date = new Date(policy.effective_date);
          const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          
          const existing = monthlyData.get(monthKey) || { total: 0, count: 0 };
          existing.total += policy.premium_amount;
          existing.count += 1;
          monthlyData.set(monthKey, existing);
        }
      });

      return Array.from(monthlyData.entries())
        .map(([month, data]) => ({
          month,
          total_premium: data.total,
          average_premium: data.count > 0 ? data.total / data.count : 0,
          policy_count: data.count
        }))
        .sort((a, b) => a.month.localeCompare(b.month))
        .slice(-12); // Last 12 months
    } catch (error) {
      console.error('Error fetching premium analytics:', error);
      throw error;
    }
  }

  async getDocumentProcessingStats(): Promise<DocumentProcessingStats[]> {
    try {
      const documents = await documentApi.getDocuments();
      const statusCounts = new Map<string, number>();

      documents.forEach(doc => {
        const status = doc.processing_status || 'pending';
        statusCounts.set(status, (statusCounts.get(status) || 0) + 1);
      });

      const total = documents.length;
      return Array.from(statusCounts.entries()).map(([status, count]) => ({
        status: status.charAt(0).toUpperCase() + status.slice(1),
        count,
        percentage: total > 0 ? Math.round((count / total) * 100) : 0,
        color: this.PROCESSING_STATUS_COLORS[status as keyof typeof this.PROCESSING_STATUS_COLORS] || this.PROCESSING_STATUS_COLORS.pending
      }));
    } catch (error) {
      console.error('Error fetching document processing stats:', error);
      throw error;
    }
  }

  async getTrendData(days: number = 30): Promise<TrendData[]> {
    try {
      const [policies, documents, carriers] = await Promise.all([
        policyApi.getPolicies(),
        documentApi.getDocuments(),
        carrierApi.getCarriers()
      ]);

      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - days);

      const trendData: TrendData[] = [];
      
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().split('T')[0];
        
        const policiesOnDate = policies.filter(p => 
          p.created_at && new Date(p.created_at).toISOString().split('T')[0] === dateStr
        ).length;
        
        const documentsOnDate = documents.filter(d => 
          d.uploaded_at && new Date(d.uploaded_at).toISOString().split('T')[0] === dateStr
        ).length;
        
        const carriersOnDate = carriers.filter(c => 
          c.created_at && new Date(c.created_at).toISOString().split('T')[0] === dateStr
        ).length;

        trendData.push({
          date: dateStr,
          policies: policiesOnDate,
          documents: documentsOnDate,
          carriers: carriersOnDate
        });
      }

      return trendData;
    } catch (error) {
      console.error('Error fetching trend data:', error);
      throw error;
    }
  }

  async getCarrierPerformance(): Promise<CarrierPerformance[]> {
    try {
      const [policies, documents, carriers] = await Promise.all([
        policyApi.getPolicies(),
        documentApi.getDocuments(),
        carrierApi.getCarriers()
      ]);

      return carriers.map(carrier => {
        const carrierPolicies = policies.filter(p => p.carrier_id === carrier.id);
        const carrierDocuments = documents.filter(d => d.carrier_id === carrier.id);
        
        const completedDocs = carrierDocuments.filter(d => d.processing_status === 'completed');
        const successRate = carrierDocuments.length > 0 ? 
          (completedDocs.length / carrierDocuments.length) * 100 : 0;

        // Calculate average processing time (mock calculation)
        const avgProcessingTime = carrierDocuments.length > 0 ? 
          Math.random() * 300 + 60 : 0; // 1-5 minutes mock

        // Recent activity (last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        
        const recentActivity = [
          ...carrierPolicies.filter(p => p.created_at && new Date(p.created_at) > sevenDaysAgo),
          ...carrierDocuments.filter(d => d.uploaded_at && new Date(d.uploaded_at) > sevenDaysAgo)
        ].length;

        return {
          carrier_id: carrier.id,
          carrier_name: carrier.name,
          carrier_code: carrier.code,
          total_policies: carrierPolicies.length,
          total_documents: carrierDocuments.length,
          avg_processing_time: Math.round(avgProcessingTime),
          success_rate: Math.round(successRate),
          recent_activity: recentActivity
        };
      }).sort((a, b) => b.total_policies - a.total_policies);
    } catch (error) {
      console.error('Error fetching carrier performance:', error);
      throw error;
    }
  }
}

export const analyticsService = new AnalyticsService();
