<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
        input { margin: 5px; padding: 8px; width: 200px; }
        textarea { width: 100%; height: 100px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>US Insurance Platform - Authentication Integration Test</h1>
    
    <div class="test-section">
        <h2>Frontend Pages Test</h2>
        <button onclick="testFrontendPages()">Test Frontend Pages</button>
        <div id="frontend-results"></div>
    </div>

    <div class="test-section">
        <h2>Backend API Test</h2>
        <button onclick="testBackendAPI()">Test Backend API</button>
        <div id="backend-results"></div>
    </div>

    <div class="test-section">
        <h2>Registration Test</h2>
        <input type="email" id="reg-email" placeholder="Email" value="<EMAIL>">
        <input type="text" id="reg-firstname" placeholder="First Name" value="Test">
        <input type="text" id="reg-lastname" placeholder="Last Name" value="User">
        <input type="password" id="reg-password" placeholder="Password" value="testpass123">
        <button onclick="testRegistration()">Test Registration</button>
        <div id="registration-results"></div>
    </div>

    <div class="test-section">
        <h2>Login Test</h2>
        <input type="email" id="login-email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="login-password" placeholder="Password" value="testpass123">
        <button onclick="testLogin()">Test Login</button>
        <div id="login-results"></div>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <textarea id="test-log" readonly></textarea>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        const FRONTEND_BASE = 'http://localhost:3000';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logArea = document.getElementById('test-log');
            logArea.value += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('test-log').value = '';
        }

        async function testFrontendPages() {
            const resultsDiv = document.getElementById('frontend-results');
            resultsDiv.innerHTML = '<p class="info">Testing frontend pages...</p>';
            
            const pages = [
                { name: 'Home', url: `${FRONTEND_BASE}/` },
                { name: 'Login', url: `${FRONTEND_BASE}/login` },
                { name: 'Register', url: `${FRONTEND_BASE}/register` }
            ];

            let results = '';
            for (const page of pages) {
                try {
                    const response = await fetch(page.url);
                    if (response.ok) {
                        results += `<p class="success">✓ ${page.name} page: OK (${response.status})</p>`;
                        log(`${page.name} page accessible`, 'success');
                    } else {
                        results += `<p class="error">✗ ${page.name} page: Error ${response.status}</p>`;
                        log(`${page.name} page error: ${response.status}`, 'error');
                    }
                } catch (error) {
                    results += `<p class="error">✗ ${page.name} page: ${error.message}</p>`;
                    log(`${page.name} page error: ${error.message}`, 'error');
                }
            }
            resultsDiv.innerHTML = results;
        }

        async function testBackendAPI() {
            const resultsDiv = document.getElementById('backend-results');
            resultsDiv.innerHTML = '<p class="info">Testing backend API...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();
                
                if (response.ok && data.status === 'healthy') {
                    resultsDiv.innerHTML = '<p class="success">✓ Backend API: Healthy</p>';
                    log('Backend API is healthy', 'success');
                } else {
                    resultsDiv.innerHTML = '<p class="error">✗ Backend API: Unhealthy</p>';
                    log('Backend API is unhealthy', 'error');
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p class="error">✗ Backend API: ${error.message}</p>`;
                log(`Backend API error: ${error.message}`, 'error');
            }
        }

        async function testRegistration() {
            const resultsDiv = document.getElementById('registration-results');
            resultsDiv.innerHTML = '<p class="info">Testing registration...</p>';
            
            const email = document.getElementById('reg-email').value;
            const firstName = document.getElementById('reg-firstname').value;
            const lastName = document.getElementById('reg-lastname').value;
            const password = document.getElementById('reg-password').value;

            try {
                const response = await fetch(`${API_BASE}/api/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        first_name: firstName,
                        last_name: lastName,
                        password: password,
                        password_confirm: password
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `<p class="success">✓ Registration successful for ${data.email}</p>`;
                    log(`Registration successful for ${data.email}`, 'success');
                } else {
                    resultsDiv.innerHTML = `<p class="error">✗ Registration failed: ${data.detail}</p>`;
                    log(`Registration failed: ${data.detail}`, 'error');
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p class="error">✗ Registration error: ${error.message}</p>`;
                log(`Registration error: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            const resultsDiv = document.getElementById('login-results');
            resultsDiv.innerHTML = '<p class="info">Testing login...</p>';
            
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            try {
                const formData = new FormData();
                formData.append('username', email);
                formData.append('password', password);

                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `<p class="success">✓ Login successful for ${data.user.email}</p>`;
                    log(`Login successful for ${data.user.email}`, 'success');
                    log(`Access token received: ${data.access_token.substring(0, 20)}...`, 'info');
                } else {
                    resultsDiv.innerHTML = `<p class="error">✗ Login failed: ${data.detail}</p>`;
                    log(`Login failed: ${data.detail}`, 'error');
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p class="error">✗ Login error: ${error.message}</p>`;
                log(`Login error: ${error.message}`, 'error');
            }
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            log('Authentication Integration Test Started', 'info');
            testFrontendPages();
            testBackendAPI();
        };
    </script>
</body>
</html>
