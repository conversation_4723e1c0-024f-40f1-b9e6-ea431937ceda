"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hoist\", ({\n    enumerable: true,\n    get: function() {\n        return hoist;\n    }\n}));\nfunction hoist(module, name) {\n    // If the name is available in the module, return it.\n    if (name in module) {\n        return module[name];\n    }\n    // If a property called `then` exists, assume it's a promise and\n    // return a promise that resolves to the name.\n    if (\"then\" in module && typeof module.then === \"function\") {\n        return module.then((mod)=>hoist(mod, name));\n    }\n    // If we're trying to hoise the default export, and the module is a function,\n    // return the module itself.\n    if (typeof module === \"function\" && name === \"default\") {\n        return module;\n    }\n    // Otherwise, return undefined.\n    return undefined;\n}\n\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9oZWxwZXJzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQWlCO0FBQ2pCLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLHlDQUF3QztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3VzLWluc3VyYW5jZS1kZXRhaWxzLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1yb3V0ZS1sb2FkZXIvaGVscGVycy5qcz85OWZkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSG9pc3RzIGEgbmFtZSBmcm9tIGEgbW9kdWxlIG9yIHByb21pc2VkIG1vZHVsZS5cbiAqXG4gKiBAcGFyYW0gbW9kdWxlIHRoZSBtb2R1bGUgdG8gaG9pc3QgdGhlIG5hbWUgZnJvbVxuICogQHBhcmFtIG5hbWUgdGhlIG5hbWUgdG8gaG9pc3RcbiAqIEByZXR1cm5zIHRoZSB2YWx1ZSBvbiB0aGUgbW9kdWxlIChvciBwcm9taXNlZCBtb2R1bGUpXG4gKi8gXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJob2lzdFwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gaG9pc3Q7XG4gICAgfVxufSk7XG5mdW5jdGlvbiBob2lzdChtb2R1bGUsIG5hbWUpIHtcbiAgICAvLyBJZiB0aGUgbmFtZSBpcyBhdmFpbGFibGUgaW4gdGhlIG1vZHVsZSwgcmV0dXJuIGl0LlxuICAgIGlmIChuYW1lIGluIG1vZHVsZSkge1xuICAgICAgICByZXR1cm4gbW9kdWxlW25hbWVdO1xuICAgIH1cbiAgICAvLyBJZiBhIHByb3BlcnR5IGNhbGxlZCBgdGhlbmAgZXhpc3RzLCBhc3N1bWUgaXQncyBhIHByb21pc2UgYW5kXG4gICAgLy8gcmV0dXJuIGEgcHJvbWlzZSB0aGF0IHJlc29sdmVzIHRvIHRoZSBuYW1lLlxuICAgIGlmIChcInRoZW5cIiBpbiBtb2R1bGUgJiYgdHlwZW9mIG1vZHVsZS50aGVuID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgcmV0dXJuIG1vZHVsZS50aGVuKChtb2QpPT5ob2lzdChtb2QsIG5hbWUpKTtcbiAgICB9XG4gICAgLy8gSWYgd2UncmUgdHJ5aW5nIHRvIGhvaXNlIHRoZSBkZWZhdWx0IGV4cG9ydCwgYW5kIHRoZSBtb2R1bGUgaXMgYSBmdW5jdGlvbixcbiAgICAvLyByZXR1cm4gdGhlIG1vZHVsZSBpdHNlbGYuXG4gICAgaWYgKHR5cGVvZiBtb2R1bGUgPT09IFwiZnVuY3Rpb25cIiAmJiBuYW1lID09PSBcImRlZmF1bHRcIikge1xuICAgICAgICByZXR1cm4gbW9kdWxlO1xuICAgIH1cbiAgICAvLyBPdGhlcndpc2UsIHJldHVybiB1bmRlZmluZWQuXG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aGVscGVycy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?page=%2F_document&absolutePagePath=private-next-pages%2F_document&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?page=%2F_document&absolutePagePath=private-next-pages%2F_document&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module */ \"./node_modules/next/dist/server/future/route-modules/pages/module.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-route-loader/helpers */ \"./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__);\n\n        // Next.js Route Loader\n        \n        \n\n        // Import the userland code.\n        \n\n        // Re-export the component (should be the default export).\n        /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"default\"));\n\n        // Re-export methods.\n        const getStaticProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"getStaticProps\")\n        const getStaticPaths = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"getStaticPaths\")\n        const getServerSideProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"getServerSideProps\")\n        const config = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"config\")\n        const reportWebVitals = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"reportWebVitals\")\n\n        // Re-export legacy methods.\n        const unstable_getStaticProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"unstable_getStaticProps\")\n        const unstable_getStaticPaths = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"unstable_getStaticPaths\")\n        const unstable_getStaticParams = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"unstable_getStaticParams\")\n        const unstable_getServerProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"unstable_getServerProps\")\n        const unstable_getServerSideProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__, \"unstable_getServerSideProps\")\n\n        // Create and export the route module that will be consumed.\n        const options = {\"definition\":{\"kind\":\"PAGES\",\"page\":\"/_document\",\"pathname\":\"/_document\",\"bundlePath\":\"\",\"filename\":\"\"}}\n        const routeModule = new (next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0___default())({ ...options, userland: private_next_pages_document__WEBPACK_IMPORTED_MODULE_2__ })\n        \n        \n    //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?page=%2F_document&absolutePagePath=private-next-pages%2F_document&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Head: function() {\n        return Head;\n    },\n    NextScript: function() {\n        return NextScript;\n    },\n    Html: function() {\n        return Html;\n    },\n    Main: function() {\n        return Main;\n    },\n    /**\n * `Document` component handles the initial `document` markup and renders only on the server side.\n * Commonly used for implementing server side rendering for `css-in-js` libraries.\n */ default: function() {\n        return Document;\n    }\n});\nconst _react = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! react */ \"react\"));\nconst _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nconst _getpagefiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nconst _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nconst _iserror = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nconst _htmlcontext = __webpack_require__(/*! ../shared/lib/html-context */ \"../shared/lib/html-context\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\n/** Set of pages that have triggered a large data warning on production mode. */ const largePageDataWarnings = new Set();\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getpagefiles.getPageFiles)(buildManifest, \"/_app\");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getpagefiles.getPageFiles)(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix, buildManifest, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${assetQueryString}`\n        }));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var _el_props, _el_props_dangerouslySetInnerHTML;\n            return el == null ? void 0 : (_el_props = el.props) == null ? void 0 : (_el_props_dangerouslySetInnerHTML = _el_props.dangerouslySetInnerHTML) == null ? void 0 : _el_props_dangerouslySetInnerHTML.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports, assetPrefix, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var _buildManifest_lowPriorityFiles;\n    const { assetPrefix, buildManifest, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(\".js\"));\n    const lowPriorityScripts = (_buildManifest_lowPriorityFiles = buildManifest.lowPriorityFiles) == null ? void 0 : _buildManifest_lowPriorityFiles.filter((file)=>file.endsWith(\".js\"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix, scriptLoader, crossOrigin, nextScriptWorkers } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n    try {\n        let { partytownSnippet } = require(\"@builder.io/partytown/integration\");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var _child_props, _child_props_dangerouslySetInnerHTML;\n            return hasComponentProps(child) && (child == null ? void 0 : (_child_props = child.props) == null ? void 0 : (_child_props_dangerouslySetInnerHTML = _child_props.dangerouslySetInnerHTML) == null ? void 0 : _child_props_dangerouslySetInnerHTML.__html.length) && \"data-partytown-config\" in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown-config\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy, src, children: scriptChildren, dangerouslySetInnerHTML, ...scriptProps } = file;\n            let srcProps = {};\n            if (src) {\n                // Use external src if provided\n                srcProps.src = src;\n            } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                // Embed inline script if provided with dangerouslySetInnerHTML\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: dangerouslySetInnerHTML.__html\n                };\n            } else if (scriptChildren) {\n                // Embed inline script if provided with children\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n                };\n            } else {\n                throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                ...srcProps,\n                ...scriptProps,\n                type: \"text/partytown\",\n                key: src || index,\n                nonce: props.nonce,\n                \"data-nscript\": \"worker\",\n                crossOrigin: props.crossOrigin || crossOrigin\n            });\n        }));\n    } catch (err) {\n        if ((0, _iserror.default)(err) && err.code !== \"MODULE_NOT_FOUND\") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader, disableOptimizedLoading, crossOrigin } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy, ...scriptProps } = file;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            ...scriptProps,\n            key: scriptProps.src || index,\n            defer: scriptProps.defer ?? !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin, nonce, ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nfunction getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix = \"\", assetQueryString = \"\") {\n    if (!nextFontManifest) {\n        return {\n            preconnect: null,\n            preload: null\n        };\n    }\n    const appFontsEntry = nextFontManifest.pages[\"/_app\"];\n    const pageFontsEntry = nextFontManifest.pages[dangerousAsPath];\n    const preloadedFontFiles = [\n        ...appFontsEntry ?? [],\n        ...pageFontsEntry ?? []\n    ];\n    // If no font files should preload but there's an entry for the path, add a preconnect tag.\n    const preconnectToSelf = !!(preloadedFontFiles.length === 0 && (appFontsEntry || pageFontsEntry));\n    // we only add if the dpl query is present for fonts\n    if (!assetQueryString.includes(\"dpl=\")) {\n        assetQueryString = \"\";\n    }\n    return {\n        preconnect: preconnectToSelf ? /*#__PURE__*/ _react.default.createElement(\"link\", {\n            \"data-next-font\": nextFontManifest.pagesUsingSizeAdjust ? \"size-adjust\" : \"\",\n            rel: \"preconnect\",\n            href: \"/\",\n            crossOrigin: \"anonymous\"\n        }) : null,\n        preload: preloadedFontFiles ? preloadedFontFiles.map((fontFile)=>{\n            const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)[1];\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: fontFile,\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${encodeURI(fontFile)}${assetQueryString}`,\n                as: \"font\",\n                type: `font/${ext}`,\n                crossOrigin: \"anonymous\",\n                \"data-next-font\": fontFile.includes(\"-s\") ? \"size-adjust\" : \"\"\n            });\n        }) : null\n    };\n}\nclass Head extends _react.default.Component {\n    static #_ = (()=>{\n        this.contextType = _htmlcontext.HtmlContext;\n    })();\n    getCssLinks(files) {\n        const { assetPrefix, assetQueryString, dynamicImports, crossOrigin, optimizeCss, optimizeFonts } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(\".css\"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(\".css\"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports, assetPrefix, assetQueryString, crossOrigin } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(\".js\")) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix, assetQueryString, scriptLoader, crossOrigin } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(\".js\");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }))\n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader } = this.context;\n        const { nonce, crossOrigin } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy, children, dangerouslySetInnerHTML, src, ...scriptProps } = file;\n            let html = \"\";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                ...scriptProps,\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            });\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var _c_props, _c_props1;\n            if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (_c_props = c.props) == null ? void 0 : _c_props.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url })=>{\n                var _c_props, _c_props_href;\n                return c == null ? void 0 : (_c_props = c.props) == null ? void 0 : (_c_props_href = _c_props.href) == null ? void 0 : _c_props_href.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    \"data-href\": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (_c_props1 = c.props) == null ? void 0 : _c_props1.children) {\n                const newProps = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            }\n            return c;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles, ampPath, inAmpMode, hybridAmp, canonicalBase, __NEXT_DATA__, dangerousAsPath, headTags, unstable_runtimeJS, unstable_JsPreload, disableOptimizedLoading, optimizeCss, optimizeFonts, assetPrefix, nextFontManifest } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                let metaTag;\n                if (this.context.strictNextHead) {\n                    metaTag = /*#__PURE__*/ _react.default.createElement(\"meta\", {\n                        name: \"next-head\",\n                        content: \"1\"\n                    });\n                }\n                if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n                    metaTag && cssPreloads.push(metaTag);\n                    cssPreloads.push(c);\n                } else {\n                    if (c) {\n                        if (metaTag && (c.type !== \"meta\" || !c.props[\"charSet\"])) {\n                            otherHeadElements.push(metaTag);\n                        }\n                        otherHeadElements.push(c);\n                    }\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var _child_props;\n                const isReactHelmet = child == null ? void 0 : (_child_props = child.props) == null ? void 0 : _child_props[\"data-react-helmet\"];\n                if (!isReactHelmet) {\n                    var _child_props1;\n                    if ((child == null ? void 0 : child.type) === \"title\") {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (_child_props1 = child.props) == null ? void 0 : _child_props1.name) === \"viewport\") {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n            });\n            if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type, props } = child;\n            if ( true && inAmpMode) {\n                let badProp = \"\";\n                if (type === \"meta\" && props.name === \"viewport\") {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === \"link\" && props.rel === \"canonical\") {\n                    hasCanonicalRel = true;\n                } else if (type === \"script\") {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n                        badProp = \"<script\";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += \"/>\";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === \"link\" && props.rel === \"amphtml\") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        const nextFontLinkTags = getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix, this.context.assetQueryString);\n        return /*#__PURE__*/ _react.default.createElement(\"head\", getHeadHTMLProps(this.props), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, this.context.strictNextHead ? null : /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-head-count\",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-font-preconnect\"\n        }), nextFontLinkTags.preconnect, nextFontLinkTags.preload,  true && inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"canonical\",\n            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../server/utils\").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preload\",\n            as: \"script\",\n            href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: true,\n            src: \"https://cdn.ampproject.org/v0.js\"\n        })), !( true && inAmpMode) && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"amphtml\",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": this.props.nonce ?? \"\"\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": this.props.nonce ?? \"\"\n        }), this.context.isDevelopment && // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var _children_find, _children_find_props, _children_find1, _children_find_props1;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (_children_find = children.find((child)=>child.type === Head)) == null ? void 0 : (_children_find_props = _children_find.props) == null ? void 0 : _children_find_props.children;\n    const bodyChildren = (_children_find1 = children.find((child)=>child.type === \"body\")) == null ? void 0 : (_children_find_props1 = _children_find1.props) == null ? void 0 : _children_find_props1.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ]\n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var _child_type;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((_child_type = child.type) == null ? void 0 : _child_type.__nextScript) {\n            if (child.props.strategy === \"beforeInteractive\") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }\n                ]);\n                return;\n            } else if ([\n                \"lazyOnload\",\n                \"afterInteractive\",\n                \"worker\"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static #_ = (()=>{\n        this.contextType = _htmlcontext.HtmlContext;\n    })();\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__, largePageDataBytes } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n                return (0, _htmlescape.htmlEscapeJsonString)(data);\n            }\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                if (false) {}\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape.htmlEscapeJsonString)(data);\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.message.indexOf(\"circular structure\") !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix, inAmpMode, buildManifest, unstable_runtimeJS, docComponentsRendered, assetQueryString, disableOptimizedLoading, crossOrigin } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles\n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                id: \"__NEXT_DATA__\",\n                type: \"application/json\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                \"data-ampdevmode\": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${assetQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    \"data-ampdevmode\": true\n                })));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n            id: \"__NEXT_DATA__\",\n            type: \"application/json\",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nfunction Html(props) {\n    const { inAmpMode, docComponentsRendered, locale, scriptLoader, __NEXT_DATA__ } = (0, _react.useContext)(_htmlcontext.HtmlContext);\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ _react.default.createElement(\"html\", {\n        ...props,\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? \"\" : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== \"production\" ? \"\" : undefined\n    });\n}\nfunction Main() {\n    const { docComponentsRendered } = (0, _react.useContext)(_htmlcontext.HtmlContext);\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement(\"next-js-internal-body-render-target\", null);\n}\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return isError;\n    },\n    getProperError: function() {\n        return getProperError;\n    }\n});\nconst _isplainobject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isplainobject.isPlainObject)(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return prettyBytes;\n    }\n}));\nconst UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "./node_modules/next/dist/server/future/route-modules/pages/module.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/pages/module.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PagesRouteModule: function() {\n        return PagesRouteModule;\n    },\n    default: function() {\n        return _default;\n    }\n});\nconst _routemodule = __webpack_require__(/*! ../route-module */ \"../route-module\");\nconst _render = __webpack_require__(/*! ../../../render */ \"../../../render\");\nclass PagesRouteModule extends _routemodule.RouteModule {\n    setup() {\n        throw new Error(\"Method not implemented.\");\n    }\n    handle() {\n        throw new Error(\"Method not implemented.\");\n    }\n    async render(req, res, pathname, query, renderOpts) {\n        const result = await (0, _render.renderToHTML)(req, res, pathname, query, renderOpts);\n        return result;\n    }\n}\nconst _default = PagesRouteModule;\n\n//# sourceMappingURL=module.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/future/route-modules/pages/module.js\n");

/***/ }),

/***/ "../route-module":
/*!************************************************************************!*\
  !*** external "next/dist/server/future/route-modules/route-module.js" ***!
  \************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/future/route-modules/route-module.js");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../../../render":
/*!*********************************************!*\
  !*** external "next/dist/server/render.js" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/render.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/html-context":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/html-context.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?page=%2F_document&absolutePagePath=private-next-pages%2F_document&preferredRegion=&middlewareConfig=e30%3D!"));
module.exports = __webpack_exports__;

})();