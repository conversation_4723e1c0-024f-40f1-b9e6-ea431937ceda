/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/register"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CGrowthSch%5CUSInsuranceDetails%5Cfrontend%5Csrc%5Cpages%5Cregister.tsx&page=%2Fregister!":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CGrowthSch%5CUSInsuranceDetails%5Cfrontend%5Csrc%5Cpages%5Cregister.tsx&page=%2Fregister! ***!
  \***************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/register\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/register.tsx */ \"./src/pages/register.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/register\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDR3Jvd3RoU2NoJTVDVVNJbnN1cmFuY2VEZXRhaWxzJTVDZnJvbnRlbmQlNUNzcmMlNUNwYWdlcyU1Q3JlZ2lzdGVyLnRzeCZwYWdlPSUyRnJlZ2lzdGVyISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLDBEQUEwQjtBQUNqRDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/OWVlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL3JlZ2lzdGVyXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9zcmMvcGFnZXMvcmVnaXN0ZXIudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9yZWdpc3RlclwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CGrowthSch%5CUSInsuranceDetails%5Cfrontend%5Csrc%5Cpages%5Cregister.tsx&page=%2Fregister!\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PrefetchKind: function() {\n        return PrefetchKind;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_FAST_REFRESH: function() {\n        return ACTION_FAST_REFRESH;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    }\n});\nconst ACTION_REFRESH = \"refresh\";\nconst ACTION_NAVIGATE = \"navigate\";\nconst ACTION_RESTORE = \"restore\";\nconst ACTION_SERVER_PATCH = \"server-patch\";\nconst ACTION_PREFETCH = \"prefetch\";\nconst ACTION_FAST_REFRESH = \"fast-refresh\";\nconst ACTION_SERVER_ACTION = \"server-action\";\nvar PrefetchKind;\n(function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n})(PrefetchKind || (PrefetchKind = {}));\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ../shared/lib/router/utils/resolve-href */ \"./node_modules/next/dist/shared/lib/router/utils/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _routercontext = __webpack_require__(/*! ../shared/lib/router-context */ \"./node_modules/next/dist/shared/lib/router-context.js\");\nconst _approutercontext = __webpack_require__(/*! ../shared/lib/app-router-context */ \"./node_modules/next/dist/shared/lib/app-router-context.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (false) {}\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const prefetchPromise = isAppRouter ? router.prefetch(href, appOptions) : router.prefetch(href, as, options);\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    Promise.resolve(prefetchPromise).catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter, prefetchEnabled) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                forceOptimisticNavigation: !prefetchEnabled\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * React Component that enables client-side transitions between routes.\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = true === false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ _react.default.createElement(\"a\", null, children);\n    }\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    const pagesRouter = _react.default.useContext(_routercontext.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontext.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter, prefetchEnabled);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart (e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ _react.default.createElement(\"a\", {\n        ...restProps,\n        ...childProps\n    }, children);\n}, \"iGdYZW22TllPihoUFJYL0qIKigo=\")), \"iGdYZW22TllPihoUFJYL0qIKigo=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProtectedRoute: function() { return /* binding */ ProtectedRoute; },\n/* harmony export */   withAuth: function() { return /* binding */ withAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\nconst ProtectedRoute = (param)=>{\n    let { children, redirectTo = \"/login\", requireAuth = true } = param;\n    _s();\n    const { user, loading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading) {\n            if (requireAuth && !isAuthenticated) {\n                // Store the current path for redirect after login\n                const currentPath = router.asPath;\n                router.push(\"\".concat(redirectTo, \"?redirect=\").concat(encodeURIComponent(currentPath)));\n            } else if (!requireAuth && isAuthenticated) {\n                // Redirect authenticated users away from auth pages\n                router.push(\"/dashboard\");\n            }\n        }\n    }, [\n        loading,\n        isAuthenticated,\n        requireAuth,\n        router,\n        redirectTo\n    ]);\n    // Show loading spinner while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Don't render children if authentication requirements aren't met\n    if (requireAuth && !isAuthenticated) {\n        return null;\n    }\n    if (!requireAuth && isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n_s(ProtectedRoute, \"RSKdTqqa8sIcSl0j7/tbD2lfdCw=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ProtectedRoute;\n// Higher-order component for protecting pages\nconst withAuth = function(WrappedComponent) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { requireAuth = true, redirectTo = \"/login\" } = options;\n    const AuthenticatedComponent = (props)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProtectedRoute, {\n            requireAuth: requireAuth,\n            redirectTo: redirectTo,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n                ...props\n            }, void 0, false, {\n                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, _this)\n        }, void 0, false, {\n            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, _this);\n    };\n    AuthenticatedComponent.displayName = \"withAuth(\".concat(WrappedComponent.displayName || WrappedComponent.name, \")\");\n    return AuthenticatedComponent;\n};\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/auth/ProtectedRoute.tsx\n"));

/***/ }),

/***/ "./src/components/auth/RegisterForm.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/RegisterForm.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RegisterForm: function() { return /* binding */ RegisterForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst RegisterForm = (param)=>{\n    let { onSuccess } = param;\n    _s();\n    const { register, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        password_confirm: \"\",\n        first_name: \"\",\n        last_name: \"\",\n        company_name: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [submitError, setSubmitError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Email validation\n        if (!formData.email) {\n            newErrors.email = \"Email is required\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Please enter a valid email address\";\n        }\n        // Name validation\n        if (!formData.first_name) {\n            newErrors.first_name = \"First name is required\";\n        }\n        if (!formData.last_name) {\n            newErrors.last_name = \"Last name is required\";\n        }\n        // Password validation\n        if (!formData.password) {\n            newErrors.password = \"Password is required\";\n        } else if (formData.password.length < 8) {\n            newErrors.password = \"Password must be at least 8 characters long\";\n        }\n        // Password confirmation validation\n        if (!formData.password_confirm) {\n            newErrors.password_confirm = \"Please confirm your password\";\n        } else if (formData.password !== formData.password_confirm) {\n            newErrors.password_confirm = \"Passwords do not match\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear field error when user starts typing\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: \"\"\n                }));\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setSubmitError(\"\");\n        if (!validateForm()) {\n            return;\n        }\n        try {\n            await register(formData);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        } catch (error) {\n            setSubmitError(error.message);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"Create your account\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: [\n                                \"Or\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n                                    children: \"sign in to your existing account\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        submitError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md bg-red-50 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-red-700\",\n                                children: submitError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"first_name\",\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"First Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"first_name\",\n                                                    name: \"first_name\",\n                                                    type: \"text\",\n                                                    autoComplete: \"given-name\",\n                                                    required: true,\n                                                    className: \"mt-1 appearance-none relative block w-full px-3 py-2 border \".concat(errors.first_name ? \"border-red-300\" : \"border-gray-300\", \" placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"),\n                                                    placeholder: \"First name\",\n                                                    value: formData.first_name,\n                                                    onChange: handleInputChange\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.first_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.first_name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"last_name\",\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"Last Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"last_name\",\n                                                    name: \"last_name\",\n                                                    type: \"text\",\n                                                    autoComplete: \"family-name\",\n                                                    required: true,\n                                                    className: \"mt-1 appearance-none relative block w-full px-3 py-2 border \".concat(errors.last_name ? \"border-red-300\" : \"border-gray-300\", \" placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"),\n                                                    placeholder: \"Last name\",\n                                                    value: formData.last_name,\n                                                    onChange: handleInputChange\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.last_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.last_name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            className: \"mt-1 appearance-none relative block w-full px-3 py-2 border \".concat(errors.email ? \"border-red-300\" : \"border-gray-300\", \" placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"),\n                                            placeholder: \"Email address\",\n                                            value: formData.email,\n                                            onChange: handleInputChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.email\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"company_name\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Company Name (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"company_name\",\n                                            name: \"company_name\",\n                                            type: \"text\",\n                                            autoComplete: \"organization\",\n                                            className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                            placeholder: \"Company name\",\n                                            value: formData.company_name,\n                                            onChange: handleInputChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: \"password\",\n                                            autoComplete: \"new-password\",\n                                            required: true,\n                                            className: \"mt-1 appearance-none relative block w-full px-3 py-2 border \".concat(errors.password ? \"border-red-300\" : \"border-gray-300\", \" placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"),\n                                            placeholder: \"Password (min. 8 characters)\",\n                                            value: formData.password,\n                                            onChange: handleInputChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.password\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password_confirm\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password_confirm\",\n                                            name: \"password_confirm\",\n                                            type: \"password\",\n                                            autoComplete: \"new-password\",\n                                            required: true,\n                                            className: \"mt-1 appearance-none relative block w-full px-3 py-2 border \".concat(errors.password_confirm ? \"border-red-300\" : \"border-gray-300\", \" placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"),\n                                            placeholder: \"Confirm password\",\n                                            value: formData.password_confirm,\n                                            onChange: handleInputChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.password_confirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.password_confirm\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Creating account...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 17\n                                }, undefined) : \"Create account\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\RegisterForm.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RegisterForm, \"G2LxbmvRkbiYxkMZYtHjSrSVRjI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = RegisterForm;\nvar _c;\n$RefreshReg$(_c, \"RegisterForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/auth/RegisterForm.tsx\n"));

/***/ }),

/***/ "./src/pages/register.tsx":
/*!********************************!*\
  !*** ./src/pages/register.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_auth_RegisterForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/auth/RegisterForm */ \"./src/components/auth/RegisterForm.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/auth/ProtectedRoute */ \"./src/components/auth/ProtectedRoute.tsx\");\n\n\n\n\n\nconst RegisterPage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__.ProtectedRoute, {\n        requireAuth: false,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Create Account - US Insurance Platform\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\register.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Create your US Insurance Platform account\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\register.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\register.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RegisterForm__WEBPACK_IMPORTED_MODULE_3__.RegisterForm, {}, void 0, false, {\n                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\register.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\register.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = RegisterPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RegisterPage);\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvcmVnaXN0ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBMEI7QUFDRztBQUNrQztBQUNJO0FBRW5FLE1BQU1JLGVBQXlCO0lBQzdCLHFCQUNFLDhEQUFDRCwyRUFBY0E7UUFBQ0UsYUFBYTs7MEJBQzNCLDhEQUFDSixrREFBSUE7O2tDQUNILDhEQUFDSztrQ0FBTTs7Ozs7O2tDQUNQLDhEQUFDQzt3QkFBS0MsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7Ozs7Ozs7OzBCQUVuQyw4REFBQ1AsdUVBQVlBOzs7Ozs7Ozs7OztBQUduQjtLQVZNRTtBQVlOLCtEQUFlQSxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9wYWdlcy9yZWdpc3Rlci50c3g/NzA1MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnO1xyXG5pbXBvcnQgeyBSZWdpc3RlckZvcm0gfSBmcm9tICcuLi9jb21wb25lbnRzL2F1dGgvUmVnaXN0ZXJGb3JtJztcclxuaW1wb3J0IHsgUHJvdGVjdGVkUm91dGUgfSBmcm9tICcuLi9jb21wb25lbnRzL2F1dGgvUHJvdGVjdGVkUm91dGUnO1xyXG5cclxuY29uc3QgUmVnaXN0ZXJQYWdlOiBSZWFjdC5GQyA9ICgpID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPFByb3RlY3RlZFJvdXRlIHJlcXVpcmVBdXRoPXtmYWxzZX0+XHJcbiAgICAgIDxIZWFkPlxyXG4gICAgICAgIDx0aXRsZT5DcmVhdGUgQWNjb3VudCAtIFVTIEluc3VyYW5jZSBQbGF0Zm9ybTwvdGl0bGU+XHJcbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIkNyZWF0ZSB5b3VyIFVTIEluc3VyYW5jZSBQbGF0Zm9ybSBhY2NvdW50XCIgLz5cclxuICAgICAgPC9IZWFkPlxyXG4gICAgICA8UmVnaXN0ZXJGb3JtIC8+XHJcbiAgICA8L1Byb3RlY3RlZFJvdXRlPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBSZWdpc3RlclBhZ2U7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkhlYWQiLCJSZWdpc3RlckZvcm0iLCJQcm90ZWN0ZWRSb3V0ZSIsIlJlZ2lzdGVyUGFnZSIsInJlcXVpcmVBdXRoIiwidGl0bGUiLCJtZXRhIiwibmFtZSIsImNvbnRlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/register.tsx\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzPzc1YjMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CGrowthSch%5CUSInsuranceDetails%5Cfrontend%5Csrc%5Cpages%5Cregister.tsx&page=%2Fregister!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);