/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/carriers";
exports.ids = ["pages/carriers"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hoist\", ({\n    enumerable: true,\n    get: function() {\n        return hoist;\n    }\n}));\nfunction hoist(module, name) {\n    // If the name is available in the module, return it.\n    if (name in module) {\n        return module[name];\n    }\n    // If a property called `then` exists, assume it's a promise and\n    // return a promise that resolves to the name.\n    if (\"then\" in module && typeof module.then === \"function\") {\n        return module.then((mod)=>hoist(mod, name));\n    }\n    // If we're trying to hoise the default export, and the module is a function,\n    // return the module itself.\n    if (typeof module === \"function\" && name === \"default\") {\n        return module;\n    }\n    // Otherwise, return undefined.\n    return undefined;\n}\n\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?page=%2Fcarriers&absolutePagePath=.%2Fsrc%5Cpages%5Ccarriers%5Cindex.tsx&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?page=%2Fcarriers&absolutePagePath=.%2Fsrc%5Cpages%5Ccarriers%5Cindex.tsx&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module */ \"./node_modules/next/dist/server/future/route-modules/pages/module.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-route-loader/helpers */ \"./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js\");\n/* harmony import */ var _src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src\\pages\\carriers\\index.tsx */ \"./src/pages/carriers/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__]);\n_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n        // Next.js Route Loader\n        \n        \n\n        // Import the userland code.\n        \n\n        // Re-export the component (should be the default export).\n        /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"default\"));\n\n        // Re-export methods.\n        const getStaticProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"getStaticProps\")\n        const getStaticPaths = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"getStaticPaths\")\n        const getServerSideProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"getServerSideProps\")\n        const config = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"config\")\n        const reportWebVitals = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"reportWebVitals\")\n\n        // Re-export legacy methods.\n        const unstable_getStaticProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"unstable_getStaticProps\")\n        const unstable_getStaticPaths = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"unstable_getStaticPaths\")\n        const unstable_getStaticParams = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"unstable_getStaticParams\")\n        const unstable_getServerProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"unstable_getServerProps\")\n        const unstable_getServerSideProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_1__.hoist)(_src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__, \"unstable_getServerSideProps\")\n\n        // Create and export the route module that will be consumed.\n        const options = {\"definition\":{\"kind\":\"PAGES\",\"page\":\"/carriers\",\"pathname\":\"/carriers\",\"bundlePath\":\"\",\"filename\":\"\"}}\n        const routeModule = new (next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0___default())({ ...options, userland: _src_pages_carriers_index_tsx__WEBPACK_IMPORTED_MODULE_2__ })\n        \n        \n    \n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?page=%2Fcarriers&absolutePagePath=.%2Fsrc%5Cpages%5Ccarriers%5Cindex.tsx&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "./node_modules/next/dist/client/add-base-path.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/add-base-path.js ***!
  \********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"addBasePath\", ({\n    enumerable: true,\n    get: function() {\n        return addBasePath;\n    }\n}));\nconst _addpathprefix = __webpack_require__(/*! ../shared/lib/router/utils/add-path-prefix */ \"../shared/lib/router/utils/add-path-prefix\");\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction addBasePath(path, required) {\n    if (false) {}\n    return (0, _normalizetrailingslash.normalizePathTrailingSlash)((0, _addpathprefix.addPathPrefix)(path, basePath));\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=add-base-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/add-base-path.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/add-locale.js":
/*!*****************************************************!*\
  !*** ./node_modules/next/dist/client/add-locale.js ***!
  \*****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"addLocale\", ({\n    enumerable: true,\n    get: function() {\n        return addLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst addLocale = function(path) {\n    for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        args[_key - 1] = arguments[_key];\n    }\n    if (false) {}\n    return path;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=add-locale.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/add-locale.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PrefetchKind: function() {\n        return PrefetchKind;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_FAST_REFRESH: function() {\n        return ACTION_FAST_REFRESH;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    }\n});\nconst ACTION_REFRESH = \"refresh\";\nconst ACTION_NAVIGATE = \"navigate\";\nconst ACTION_RESTORE = \"restore\";\nconst ACTION_SERVER_PATCH = \"server-patch\";\nconst ACTION_PREFETCH = \"prefetch\";\nconst ACTION_FAST_REFRESH = \"fast-refresh\";\nconst ACTION_SERVER_ACTION = \"server-action\";\nvar PrefetchKind;\n(function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n})(PrefetchKind || (PrefetchKind = {}));\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/cjs/_interop_require_default.cjs\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"react\"));\nconst _resolvehref = __webpack_require__(/*! ../shared/lib/router/utils/resolve-href */ \"../shared/lib/router/utils/resolve-href\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"../shared/lib/router/utils/is-local-url\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"../shared/lib/router/utils/format-url\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"../shared/lib/utils\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _routercontext = __webpack_require__(/*! ../shared/lib/router-context */ \"../shared/lib/router-context\");\nconst _approutercontext = __webpack_require__(/*! ../shared/lib/app-router-context */ \"../shared/lib/app-router-context\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (true) {\n        return;\n    }\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const prefetchPromise = isAppRouter ? router.prefetch(href, appOptions) : router.prefetch(href, as, options);\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    Promise.resolve(prefetchPromise).catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter, prefetchEnabled) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                forceOptimisticNavigation: !prefetchEnabled\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * React Component that enables client-side transitions between routes.\n */ const Link = /*#__PURE__*/ _react.default.forwardRef(function LinkComponent(props, forwardedRef) {\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = true === false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ _react.default.createElement(\"a\", null, children);\n    }\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    const pagesRouter = _react.default.useContext(_routercontext.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontext.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( false ? 0 : \"\"));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( false ? 0 : \"\"));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter, prefetchEnabled);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart (e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ _react.default.createElement(\"a\", {\n        ...restProps,\n        ...childProps\n    }, children);\n});\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/normalize-trailing-slash.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/normalize-trailing-slash.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"normalizePathTrailingSlash\", ({\n    enumerable: true,\n    get: function() {\n        return normalizePathTrailingSlash;\n    }\n}));\nconst _removetrailingslash = __webpack_require__(/*! ../shared/lib/router/utils/remove-trailing-slash */ \"../shared/lib/router/utils/remove-trailing-slash\");\nconst _parsepath = __webpack_require__(/*! ../shared/lib/router/utils/parse-path */ \"../shared/lib/router/utils/parse-path\");\nconst normalizePathTrailingSlash = (path)=>{\n    if (!path.startsWith(\"/\") || undefined) {\n        return path;\n    }\n    const { pathname, query, hash } = (0, _parsepath.parsePath)(path);\n    if (false) {}\n    return \"\" + (0, _removetrailingslash.removeTrailingSlash)(pathname) + query + hash;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=normalize-trailing-slash.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/normalize-trailing-slash.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    requestIdleCallback: function() {\n        return requestIdleCallback;\n    },\n    cancelIdleCallback: function() {\n        return cancelIdleCallback;\n    }\n});\nconst requestIdleCallback = typeof self !== \"undefined\" && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return self.setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nconst cancelIdleCallback = typeof self !== \"undefined\" && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=request-idle-callback.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/request-idle-callback.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"react\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n");

/***/ }),

/***/ "./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst ProtectedRoute = ({ children, redirectTo = \"/login\", requireAuth = true })=>{\n    const { user, loading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading) {\n            if (requireAuth && !isAuthenticated) {\n                // Store the current path for redirect after login\n                const currentPath = router.asPath;\n                router.push(`${redirectTo}?redirect=${encodeURIComponent(currentPath)}`);\n            } else if (!requireAuth && isAuthenticated) {\n                // Redirect authenticated users away from auth pages\n                router.push(\"/dashboard\");\n            }\n        }\n    }, [\n        loading,\n        isAuthenticated,\n        requireAuth,\n        router,\n        redirectTo\n    ]);\n    // Show loading spinner while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Don't render children if authentication requirements aren't met\n    if (requireAuth && !isAuthenticated) {\n        return null;\n    }\n    if (!requireAuth && isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n// Higher-order component for protecting pages\nconst withAuth = (WrappedComponent, options = {})=>{\n    const { requireAuth = true, redirectTo = \"/login\" } = options;\n    const AuthenticatedComponent = (props)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProtectedRoute, {\n            requireAuth: requireAuth,\n            redirectTo: redirectTo,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n                ...props\n            }, void 0, false, {\n                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined);\n    };\n    AuthenticatedComponent.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;\n    return AuthenticatedComponent;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/authService */ \"./src/services/authService.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_authService__WEBPACK_IMPORTED_MODULE_3__]);\n_services_authService__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Create context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Provider component\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user is authenticated on app load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    const checkAuth = async ()=>{\n        try {\n            const token = _services_authService__WEBPACK_IMPORTED_MODULE_3__.authService.getToken();\n            if (token) {\n                const userData = await _services_authService__WEBPACK_IMPORTED_MODULE_3__.authService.getCurrentUser();\n                setUser(userData);\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n            _services_authService__WEBPACK_IMPORTED_MODULE_3__.authService.removeToken();\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            setLoading(true);\n            const response = await _services_authService__WEBPACK_IMPORTED_MODULE_3__.authService.login(email, password);\n            setUser(response.user);\n            // Redirect to dashboard or intended page\n            const redirectTo = router.query.redirect || \"/dashboard\";\n            router.push(redirectTo);\n        } catch (error) {\n            setLoading(false);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            setLoading(true);\n            const response = await _services_authService__WEBPACK_IMPORTED_MODULE_3__.authService.register(userData);\n            setUser(response);\n            // Redirect to dashboard after successful registration\n            router.push(\"/dashboard\");\n        } catch (error) {\n            setLoading(false);\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        _services_authService__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n        setUser(null);\n        router.push(\"/login\");\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        register,\n        logout,\n        isAuthenticated: !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook to use auth context\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "./src/pages/carriers/index.tsx":
/*!**************************************!*\
  !*** ./src/pages/carriers/index.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CarriersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/auth/ProtectedRoute */ \"./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _services_apiService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/apiService */ \"./src/services/apiService.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__, _services_apiService__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__, _services_apiService__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction CarriersPage() {\n    const [carriers, setCarriers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCarriers, setSelectedCarriers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Filter states\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"name\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadCarriers();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Apply filters when they change\n        const newFilters = {};\n        if (searchTerm) newFilters.search = searchTerm;\n        if (statusFilter === \"active\") newFilters.is_active = true;\n        if (statusFilter === \"inactive\") newFilters.is_active = false;\n        setFilters(newFilters);\n        loadCarriers(newFilters);\n    }, [\n        searchTerm,\n        statusFilter\n    ]);\n    const loadCarriers = async (filters)=>{\n        try {\n            setLoading(true);\n            const carriersData = await _services_apiService__WEBPACK_IMPORTED_MODULE_4__.carrierApi.getCarriers(filters);\n            setCarriers(carriersData);\n            setError(null);\n        } catch (err) {\n            console.error(\"Error loading carriers:\", err);\n            setError(\"Failed to load carriers\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSort = (field)=>{\n        if (sortBy === field) {\n            setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortBy(field);\n            setSortOrder(\"asc\");\n        }\n    };\n    const getSortedCarriers = ()=>{\n        const sorted = [\n            ...carriers\n        ].sort((a, b)=>{\n            let aValue;\n            let bValue;\n            switch(sortBy){\n                case \"name\":\n                    aValue = a.name.toLowerCase();\n                    bValue = b.name.toLowerCase();\n                    break;\n                case \"code\":\n                    aValue = a.code.toLowerCase();\n                    bValue = b.code.toLowerCase();\n                    break;\n                case \"date\":\n                    aValue = new Date(a.created_at).getTime();\n                    bValue = new Date(b.created_at).getTime();\n                    break;\n                default:\n                    return 0;\n            }\n            if (aValue < bValue) return sortOrder === \"asc\" ? -1 : 1;\n            if (aValue > bValue) return sortOrder === \"asc\" ? 1 : -1;\n            return 0;\n        });\n        return sorted;\n    };\n    const getStatusBadge = (isActive)=>{\n        return isActive ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\";\n    };\n    const getStatusIcon = (isActive)=>{\n        return isActive ? \"✅\" : \"❌\";\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const handleSelectCarrier = (carrierId)=>{\n        setSelectedCarriers((prev)=>prev.includes(carrierId) ? prev.filter((id)=>id !== carrierId) : [\n                ...prev,\n                carrierId\n            ]);\n    };\n    const handleSelectAll = ()=>{\n        if (selectedCarriers.length === carriers.length) {\n            setSelectedCarriers([]);\n        } else {\n            setSelectedCarriers(carriers.map((c)=>c.id));\n        }\n    };\n    const handleBulkStatusChange = async (isActive)=>{\n        if (selectedCarriers.length === 0) return;\n        const action = isActive ? \"activate\" : \"deactivate\";\n        if (!confirm(`Are you sure you want to ${action} ${selectedCarriers.length} carrier(s)?`)) {\n            return;\n        }\n        try {\n            // Update carriers one by one (could be optimized with bulk update API)\n            await Promise.all(selectedCarriers.map((id)=>_services_apiService__WEBPACK_IMPORTED_MODULE_4__.carrierApi.updateCarrier(id, {\n                    is_active: isActive\n                })));\n            // Reload carriers\n            await loadCarriers(filters);\n            setSelectedCarriers([]);\n        } catch (err) {\n            console.error(\"Error updating carriers:\", err);\n            setError(\"Failed to update some carriers\");\n        }\n    };\n    const getSortIcon = (field)=>{\n        if (sortBy !== field) return \"↕️\";\n        return sortOrder === \"asc\" ? \"↑\" : \"↓\";\n    };\n    const sortedCarriers = getSortedCarriers();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__.ProtectedRoute, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__.ProtectedRoute, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"Insurance Carriers\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mt-1\",\n                                            children: \"Manage insurance carrier relationships and integrations\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/carriers/new\",\n                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n                                    children: \"Add Carrier\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 bg-red-50 border border-red-200 rounded-md p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Filters\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Search Carriers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        placeholder: \"Search by name or code...\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: statusFilter,\n                                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All statuses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"active\",\n                                                                children: \"Active\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"inactive\",\n                                                                children: \"Inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Sort By\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: `${sortBy}-${sortOrder}`,\n                                                        onChange: (e)=>{\n                                                            const [field, order] = e.target.value.split(\"-\");\n                                                            setSortBy(field);\n                                                            setSortOrder(order);\n                                                        },\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"name-asc\",\n                                                                children: \"Name A-Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"name-desc\",\n                                                                children: \"Name Z-A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"code-asc\",\n                                                                children: \"Code A-Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"code-desc\",\n                                                                children: \"Code Z-A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"date-desc\",\n                                                                children: \"Newest first\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"date-asc\",\n                                                                children: \"Oldest first\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setSearchTerm(\"\");\n                                                        setStatusFilter(\"\");\n                                                        setSortBy(\"name\");\n                                                        setSortOrder(\"asc\");\n                                                    },\n                                                    className: \"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    children: \"Clear Filters\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        selectedCarriers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-800\",\n                                        children: [\n                                            selectedCarriers.length,\n                                            \" carrier(s) selected\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleBulkStatusChange(true),\n                                                className: \"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500\",\n                                                children: \"Activate\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleBulkStatusChange(false),\n                                                className: \"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500\",\n                                                children: \"Deactivate\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg overflow-hidden\",\n                            children: carriers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-4\",\n                                        children: \"\\uD83C\\uDFE2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No carriers found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: Object.keys(filters).length > 0 ? \"No carriers match your current filters.\" : \"Get started by adding your first insurance carrier.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/carriers/new\",\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n                                        children: \"Add Carrier\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedCarriers.length === carriers.length,\n                                                            onChange: handleSelectAll,\n                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                                        onClick: ()=>handleSort(\"name\"),\n                                                        children: [\n                                                            \"Carrier \",\n                                                            getSortIcon(\"name\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                                        onClick: ()=>handleSort(\"code\"),\n                                                        children: [\n                                                            \"Code \",\n                                                            getSortIcon(\"code\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"API Integration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                                        onClick: ()=>handleSort(\"date\"),\n                                                        children: [\n                                                            \"Created \",\n                                                            getSortIcon(\"date\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: sortedCarriers.map((carrier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedCarriers.includes(carrier.id),\n                                                                onChange: ()=>handleSelectCarrier(carrier.id),\n                                                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    carrier.logo_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: carrier.logo_url,\n                                                                        alt: carrier.name,\n                                                                        className: \"h-8 w-8 rounded-full mr-3\",\n                                                                        onError: (e)=>{\n                                                                            e.currentTarget.style.display = \"none\";\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center mr-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500 text-xs font-medium\",\n                                                                            children: carrier.name.charAt(0).toUpperCase()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: carrier.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900 font-mono\",\n                                                            children: carrier.code\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(carrier.is_active)}`,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-1\",\n                                                                        children: getStatusIcon(carrier.is_active)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    carrier.is_active ? \"Active\" : \"Inactive\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900\",\n                                                            children: carrier.api_endpoint ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-600\",\n                                                                children: \"✓ Configured\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Not configured\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900\",\n                                                            children: formatDate(carrier.created_at)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm font-medium\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                        href: `/carriers/${carrier.id}`,\n                                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                                        children: \"View\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                        href: `/carriers/${carrier.id}/edit`,\n                                                                        className: \"text-indigo-600 hover:text-indigo-900\",\n                                                                        children: \"Edit\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, carrier.id, true, {\n                                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 grid grid-cols-1 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: carriers.length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Total Carriers\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: carriers.filter((c)=>c.is_active).length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Active Carriers\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: carriers.filter((c)=>c.api_endpoint).length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"API Integrations\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: carriers.filter((c)=>!c.is_active).length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Inactive Carriers\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\GrowthSch\\\\USInsuranceDetails\\\\frontend\\\\src\\\\pages\\\\carriers\\\\index.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/carriers/index.tsx\n");

/***/ }),

/***/ "./src/services/apiService.ts":
/*!************************************!*\
  !*** ./src/services/apiService.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   carrierApi: () => (/* binding */ carrierApi),\n/* harmony export */   dashboardApi: () => (/* binding */ dashboardApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   documentApi: () => (/* binding */ documentApi),\n/* harmony export */   policyApi: () => (/* binding */ policyApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\n// Create axios instance with interceptors for auth\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL\n});\n// Add auth token to requests\napiClient.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(\"access_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n});\n// Handle token refresh on 401\napiClient.interceptors.response.use((response)=>response, async (error)=>{\n    if (error.response?.status === 401) {\n        // Try to refresh token\n        const refreshToken = localStorage.getItem(\"refresh_token\");\n        if (refreshToken) {\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_BASE_URL}/api/auth/refresh-token`, {\n                    refresh_token: refreshToken\n                });\n                const { access_token } = response.data;\n                localStorage.setItem(\"access_token\", access_token);\n                // Retry original request\n                error.config.headers.Authorization = `Bearer ${access_token}`;\n                return apiClient.request(error.config);\n            } catch (refreshError) {\n                // Refresh failed, redirect to login\n                localStorage.removeItem(\"access_token\");\n                localStorage.removeItem(\"refresh_token\");\n                window.location.href = \"/login\";\n            }\n        } else {\n            // No refresh token, redirect to login\n            window.location.href = \"/login\";\n        }\n    }\n    return Promise.reject(error);\n});\n// Policy API\nconst policyApi = {\n    // Get all policies for current user\n    async getPolicies (filters) {\n        const params = new URLSearchParams();\n        if (filters?.policy_type) params.append(\"policy_type\", filters.policy_type);\n        if (filters?.carrier_id) params.append(\"carrier_id\", filters.carrier_id);\n        if (filters?.search) params.append(\"search\", filters.search);\n        const response = await apiClient.get(`/api/policies?${params.toString()}`);\n        return response.data;\n    },\n    // Get single policy by ID\n    async getPolicy (policyId) {\n        const response = await apiClient.get(`/api/policies/${policyId}`);\n        return response.data;\n    },\n    // Create new policy\n    async createPolicy (policy) {\n        const response = await apiClient.post(\"/api/policies\", policy);\n        return response.data;\n    },\n    // Update policy\n    async updatePolicy (policyId, updates) {\n        const response = await apiClient.put(`/api/policies/${policyId}`, updates);\n        return response.data;\n    },\n    // Update policy\n    async updatePolicy (policyId, policyData) {\n        const response = await apiClient.put(`/api/policies/${policyId}`, policyData);\n        return response.data;\n    },\n    // Delete policy\n    async deletePolicy (policyId) {\n        await apiClient.delete(`/api/policies/${policyId}`);\n    },\n    // Get benefits for policy\n    async getPolicyBenefits (policyId) {\n        const response = await apiClient.get(`/api/policies/${policyId}/benefits`);\n        return response.data;\n    },\n    // Get red flags for policy\n    async getRedFlags (policyId) {\n        const response = await apiClient.get(`/api/policies/${policyId}/red-flags`);\n        return response.data.red_flags || [];\n    }\n};\n// Document API\nconst documentApi = {\n    // Get all documents for current user\n    async getDocuments (filters) {\n        const params = new URLSearchParams();\n        if (filters?.processing_status) params.append(\"processing_status\", filters.processing_status);\n        if (filters?.carrier_id) params.append(\"carrier_id\", filters.carrier_id);\n        if (filters?.search) params.append(\"search\", filters.search);\n        const response = await apiClient.get(`/api/documents?${params.toString()}`);\n        return response.data;\n    },\n    // Get single document by ID\n    async getDocument (documentId) {\n        const response = await apiClient.get(`/api/documents/${documentId}`);\n        return response.data;\n    },\n    // Upload new document\n    async uploadDocument (file, carrierID) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        if (carrierID) {\n            formData.append(\"carrier_id\", carrierID);\n        }\n        const response = await apiClient.post(\"/api/documents/upload\", formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n        return response.data;\n    },\n    // Delete document\n    async deleteDocument (documentId) {\n        await apiClient.delete(`/api/documents/${documentId}`);\n    },\n    // Bulk delete documents\n    async bulkDeleteDocuments (documentIds) {\n        // Since there's no bulk delete endpoint, delete one by one\n        await Promise.all(documentIds.map((id)=>this.deleteDocument(id)));\n    },\n    // Get document processing status (for polling)\n    async getDocumentStatus (documentId) {\n        const response = await apiClient.get(`/api/documents/${documentId}`);\n        return {\n            processing_status: response.data.processing_status,\n            processing_error: response.data.processing_error\n        };\n    },\n    // Download document (if backend supports it)\n    async downloadDocument (documentId) {\n        const response = await apiClient.get(`/api/documents/${documentId}/download`, {\n            responseType: \"blob\"\n        });\n        return response.data;\n    }\n};\n// Carrier API\nconst carrierApi = {\n    // Get all carriers with optional filtering\n    async getCarriers (filters) {\n        const params = new URLSearchParams();\n        if (filters?.search) params.append(\"search\", filters.search);\n        if (filters?.is_active !== undefined) params.append(\"is_active\", filters.is_active.toString());\n        if (filters?.skip) params.append(\"skip\", filters.skip.toString());\n        if (filters?.limit) params.append(\"limit\", filters.limit.toString());\n        const response = await apiClient.get(`/api/carriers?${params.toString()}`);\n        return response.data;\n    },\n    // Get single carrier by ID\n    async getCarrier (carrierId) {\n        const response = await apiClient.get(`/api/carriers/${carrierId}`);\n        return response.data;\n    },\n    // Create new carrier (admin only)\n    async createCarrier (carrier) {\n        const response = await apiClient.post(\"/api/carriers\", carrier);\n        return response.data;\n    },\n    // Update carrier (admin only)\n    async updateCarrier (carrierId, carrier) {\n        const response = await apiClient.put(`/api/carriers/${carrierId}`, carrier);\n        return response.data;\n    },\n    // Delete carrier (admin only)\n    async deleteCarrier (carrierId) {\n        await apiClient.delete(`/api/carriers/${carrierId}`);\n    },\n    // Get carrier statistics\n    async getCarrierStats (carrierId) {\n        // Since there's no specific stats endpoint, we'll aggregate data\n        const [policies, documents] = await Promise.all([\n            policyApi.getPolicies({\n                carrier_id: carrierId\n            }),\n            documentApi.getDocuments({\n                carrier_id: carrierId\n            })\n        ]);\n        return {\n            total_policies: policies.length,\n            total_documents: documents.length,\n            active_policies: policies.filter((p)=>p.effective_date && new Date(p.effective_date) <= new Date()).length,\n            recent_activity: Math.max(policies.length, documents.length) // Simple activity metric\n        };\n    }\n};\n// Dashboard API\nconst dashboardApi = {\n    // Get dashboard statistics\n    async getDashboardStats () {\n        // Since there's no specific dashboard endpoint, we'll aggregate data from multiple endpoints\n        const [policies, documents, carriers] = await Promise.all([\n            policyApi.getPolicies(),\n            documentApi.getDocuments(),\n            carrierApi.getCarriers()\n        ]);\n        // Calculate statistics\n        const policiesByType = policies.reduce((acc, policy)=>{\n            const type = policy.policy_type || \"unknown\";\n            acc[type] = (acc[type] || 0) + 1;\n            return acc;\n        }, {});\n        const policiesByCarrier = policies.reduce((acc, policy)=>{\n            if (policy.carrier_id) {\n                const carrier = carriers.find((c)=>c.id === policy.carrier_id);\n                const carrierName = carrier?.name || \"Unknown\";\n                acc[carrierName] = (acc[carrierName] || 0) + 1;\n            }\n            return acc;\n        }, {});\n        // Generate recent activity from policies and documents\n        const recentActivity = [\n            ...policies.slice(0, 3).map((policy)=>({\n                    id: policy.id,\n                    type: \"policy_created\",\n                    title: `Policy Created: ${policy.policy_name}`,\n                    description: `${policy.policy_type || \"Insurance\"} policy was created`,\n                    timestamp: policy.created_at,\n                    policy_id: policy.id\n                })),\n            ...documents.slice(0, 3).map((doc)=>({\n                    id: doc.id,\n                    type: \"document_uploaded\",\n                    title: `Document Uploaded: ${doc.original_filename}`,\n                    description: `Document processing status: ${doc.processing_status}`,\n                    timestamp: doc.created_at,\n                    document_id: doc.id\n                }))\n        ].sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 5);\n        return {\n            total_policies: policies.length,\n            total_documents: documents.length,\n            policies_by_type: policiesByType,\n            policies_by_carrier: policiesByCarrier,\n            recent_activity: recentActivity,\n            red_flags_summary: {\n                total: 0,\n                by_severity: {}\n            }\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc2VydmljZXMvYXBpU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBMEI7QUFlMUIsTUFBTUMsZUFBZUMsdUJBQStCRSxJQUFJO0FBRXhELG1EQUFtRDtBQUNuRCxNQUFNQyxZQUFZTCxvREFBWU0sQ0FBQztJQUM3QkMsU0FBU047QUFDWDtBQUVBLDZCQUE2QjtBQUM3QkksVUFBVUcsYUFBYUMsUUFBUUMsSUFBSSxDQUFDQztJQUNsQyxNQUFNQyxRQUFRQyxhQUFhQyxRQUFRO0lBQ25DLElBQUlGLE9BQU87UUFDVEQsT0FBT0ksUUFBUUMsZ0JBQWdCLENBQUMsT0FBTyxFQUFFSixNQUFNLENBQUM7SUFDbEQ7SUFDQSxPQUFPRDtBQUNUO0FBRUEsOEJBQThCO0FBQzlCTixVQUFVRyxhQUFhUyxTQUFTUCxJQUM5QixDQUFDTyxXQUFhQSxVQUNkLE9BQU9DO0lBQ0wsSUFBSUEsTUFBTUQsVUFBVUUsV0FBVyxLQUFLO1FBQ2xDLHVCQUF1QjtRQUN2QixNQUFNQyxlQUFlUCxhQUFhQyxRQUFRO1FBQzFDLElBQUlNLGNBQWM7WUFDaEIsSUFBSTtnQkFDRixNQUFNSCxXQUFXLE1BQU1qQixrREFBVXFCLENBQUMsQ0FBQyxFQUFFcEIsYUFBYSx1QkFBdUIsQ0FBQyxFQUFFO29CQUMxRXFCLGVBQWVGO2dCQUNqQjtnQkFDQSxNQUFNLEVBQUVHLFlBQVksRUFBRSxHQUFHTixTQUFTTztnQkFDbENYLGFBQWFZLFFBQVEsZ0JBQWdCRjtnQkFFckMseUJBQXlCO2dCQUN6QkwsTUFBTVAsT0FBT0ksUUFBUUMsZ0JBQWdCLENBQUMsT0FBTyxFQUFFTyxhQUFhLENBQUM7Z0JBQzdELE9BQU9sQixVQUFVSSxRQUFRUyxNQUFNUDtZQUNqQyxFQUFFLE9BQU9lLGNBQWM7Z0JBQ3JCLG9DQUFvQztnQkFDcENiLGFBQWFjLFdBQVc7Z0JBQ3hCZCxhQUFhYyxXQUFXO2dCQUN4QkMsT0FBT0MsU0FBU0MsT0FBTztZQUN6QjtRQUNGLE9BQU87WUFDTCxzQ0FBc0M7WUFDdENGLE9BQU9DLFNBQVNDLE9BQU87UUFDekI7SUFDRjtJQUNBLE9BQU9DLFFBQVFDLE9BQU9kO0FBQ3hCO0FBR0YsYUFBYTtBQUNOLE1BQU1lLFlBQVk7SUFDdkIsb0NBQW9DO0lBQ3BDLE1BQU1DLGFBQVlDLE9BQXVCO1FBQ3ZDLE1BQU1DLFNBQVMsSUFBSUM7UUFDbkIsSUFBSUYsU0FBU0csYUFBYUYsT0FBT0csT0FBTyxlQUFlSixRQUFRRztRQUMvRCxJQUFJSCxTQUFTSyxZQUFZSixPQUFPRyxPQUFPLGNBQWNKLFFBQVFLO1FBQzdELElBQUlMLFNBQVNNLFFBQVFMLE9BQU9HLE9BQU8sVUFBVUosUUFBUU07UUFFckQsTUFBTXhCLFdBQVcsTUFBTVosVUFBVXFDLElBQUksQ0FBQyxjQUFjLEVBQUVOLE9BQU9PLFdBQVcsQ0FBQztRQUN6RSxPQUFPMUIsU0FBU087SUFDbEI7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTW9CLFdBQVVDLFFBQWdCO1FBQzlCLE1BQU01QixXQUFXLE1BQU1aLFVBQVVxQyxJQUFJLENBQUMsY0FBYyxFQUFFRyxTQUFTLENBQUM7UUFDaEUsT0FBTzVCLFNBQVNPO0lBQ2xCO0lBRUEsb0JBQW9CO0lBQ3BCLE1BQU1zQixjQUFhQyxNQUE2QjtRQUM5QyxNQUFNOUIsV0FBVyxNQUFNWixVQUFVZ0IsS0FBSyxpQkFBaUIwQjtRQUN2RCxPQUFPOUIsU0FBU087SUFDbEI7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBTXdCLGNBQWFILFFBQWdCLEVBQUVJLE9BQXVDO1FBQzFFLE1BQU1oQyxXQUFXLE1BQU1aLFVBQVU2QyxJQUFJLENBQUMsY0FBYyxFQUFFTCxTQUFTLENBQUMsRUFBRUk7UUFDbEUsT0FBT2hDLFNBQVNPO0lBQ2xCO0lBRUEsZ0JBQWdCO0lBQ2hCLE1BQU13QixjQUFhSCxRQUFnQixFQUFFTSxVQUFlO1FBQ2xELE1BQU1sQyxXQUFXLE1BQU1aLFVBQVU2QyxJQUFJLENBQUMsY0FBYyxFQUFFTCxTQUFTLENBQUMsRUFBRU07UUFDbEUsT0FBT2xDLFNBQVNPO0lBQ2xCO0lBRUEsZ0JBQWdCO0lBQ2hCLE1BQU00QixjQUFhUCxRQUFnQjtRQUNqQyxNQUFNeEMsVUFBVWdELE9BQU8sQ0FBQyxjQUFjLEVBQUVSLFNBQVMsQ0FBQztJQUNwRDtJQUVBLDBCQUEwQjtJQUMxQixNQUFNUyxtQkFBa0JULFFBQWdCO1FBQ3RDLE1BQU01QixXQUFXLE1BQU1aLFVBQVVxQyxJQUFJLENBQUMsY0FBYyxFQUFFRyxTQUFTLFNBQVMsQ0FBQztRQUN6RSxPQUFPNUIsU0FBU087SUFDbEI7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTStCLGFBQVlWLFFBQWdCO1FBQ2hDLE1BQU01QixXQUFXLE1BQU1aLFVBQVVxQyxJQUFJLENBQUMsY0FBYyxFQUFFRyxTQUFTLFVBQVUsQ0FBQztRQUMxRSxPQUFPNUIsU0FBU08sS0FBS2dDLGFBQWEsRUFBRTtJQUN0QztBQUNGLEVBQUU7QUFFRixlQUFlO0FBQ1IsTUFBTUMsY0FBYztJQUN6QixxQ0FBcUM7SUFDckMsTUFBTUMsY0FBYXZCLE9BQXlCO1FBQzFDLE1BQU1DLFNBQVMsSUFBSUM7UUFDbkIsSUFBSUYsU0FBU3dCLG1CQUFtQnZCLE9BQU9HLE9BQU8scUJBQXFCSixRQUFRd0I7UUFDM0UsSUFBSXhCLFNBQVNLLFlBQVlKLE9BQU9HLE9BQU8sY0FBY0osUUFBUUs7UUFDN0QsSUFBSUwsU0FBU00sUUFBUUwsT0FBT0csT0FBTyxVQUFVSixRQUFRTTtRQUVyRCxNQUFNeEIsV0FBVyxNQUFNWixVQUFVcUMsSUFBSSxDQUFDLGVBQWUsRUFBRU4sT0FBT08sV0FBVyxDQUFDO1FBQzFFLE9BQU8xQixTQUFTTztJQUNsQjtJQUVBLDRCQUE0QjtJQUM1QixNQUFNb0MsYUFBWUMsVUFBa0I7UUFDbEMsTUFBTTVDLFdBQVcsTUFBTVosVUFBVXFDLElBQUksQ0FBQyxlQUFlLEVBQUVtQixXQUFXLENBQUM7UUFDbkUsT0FBTzVDLFNBQVNPO0lBQ2xCO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1zQyxnQkFBZUMsSUFBVSxFQUFFQyxTQUFrQjtRQUNqRCxNQUFNQyxXQUFXLElBQUlDO1FBQ3JCRCxTQUFTMUIsT0FBTyxRQUFRd0I7UUFDeEIsSUFBSUMsV0FBVztZQUNiQyxTQUFTMUIsT0FBTyxjQUFjeUI7UUFDaEM7UUFFQSxNQUFNL0MsV0FBVyxNQUFNWixVQUFVZ0IsS0FBSyx5QkFBeUI0QyxVQUFVO1lBQ3ZFbEQsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7UUFDRjtRQUNBLE9BQU9FLFNBQVNPO0lBQ2xCO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU0yQyxnQkFBZU4sVUFBa0I7UUFDckMsTUFBTXhELFVBQVVnRCxPQUFPLENBQUMsZUFBZSxFQUFFUSxXQUFXLENBQUM7SUFDdkQ7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTU8scUJBQW9CQyxXQUFxQjtRQUM3QywyREFBMkQ7UUFDM0QsTUFBTXRDLFFBQVF1QyxJQUFJRCxZQUFZRSxJQUFJQyxDQUFBQSxLQUFNLElBQUksQ0FBQ0wsZUFBZUs7SUFDOUQ7SUFFQSwrQ0FBK0M7SUFDL0MsTUFBTUMsbUJBQWtCWixVQUFrQjtRQUN4QyxNQUFNNUMsV0FBVyxNQUFNWixVQUFVcUMsSUFBSSxDQUFDLGVBQWUsRUFBRW1CLFdBQVcsQ0FBQztRQUNuRSxPQUFPO1lBQ0xGLG1CQUFtQjFDLFNBQVNPLEtBQUttQztZQUNqQ2Usa0JBQWtCekQsU0FBU08sS0FBS2tEO1FBQ2xDO0lBQ0Y7SUFFQSw2Q0FBNkM7SUFDN0MsTUFBTUMsa0JBQWlCZCxVQUFrQjtRQUN2QyxNQUFNNUMsV0FBVyxNQUFNWixVQUFVcUMsSUFBSSxDQUFDLGVBQWUsRUFBRW1CLFdBQVcsU0FBUyxDQUFDLEVBQUU7WUFDNUVlLGNBQWM7UUFDaEI7UUFDQSxPQUFPM0QsU0FBU087SUFDbEI7QUFDRixFQUFFO0FBRUYsY0FBYztBQUNQLE1BQU1xRCxhQUFhO0lBQ3hCLDJDQUEyQztJQUMzQyxNQUFNQyxhQUFZM0MsT0FBd0I7UUFDeEMsTUFBTUMsU0FBUyxJQUFJQztRQUNuQixJQUFJRixTQUFTTSxRQUFRTCxPQUFPRyxPQUFPLFVBQVVKLFFBQVFNO1FBQ3JELElBQUlOLFNBQVM0QyxjQUFjQyxXQUFXNUMsT0FBT0csT0FBTyxhQUFhSixRQUFRNEMsVUFBVXBDO1FBQ25GLElBQUlSLFNBQVM4QyxNQUFNN0MsT0FBT0csT0FBTyxRQUFRSixRQUFROEMsS0FBS3RDO1FBQ3RELElBQUlSLFNBQVMrQyxPQUFPOUMsT0FBT0csT0FBTyxTQUFTSixRQUFRK0MsTUFBTXZDO1FBRXpELE1BQU0xQixXQUFXLE1BQU1aLFVBQVVxQyxJQUFJLENBQUMsY0FBYyxFQUFFTixPQUFPTyxXQUFXLENBQUM7UUFDekUsT0FBTzFCLFNBQVNPO0lBQ2xCO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU0yRCxZQUFXQyxTQUFpQjtRQUNoQyxNQUFNbkUsV0FBVyxNQUFNWixVQUFVcUMsSUFBSSxDQUFDLGNBQWMsRUFBRTBDLFVBQVUsQ0FBQztRQUNqRSxPQUFPbkUsU0FBU087SUFDbEI7SUFFQSxrQ0FBa0M7SUFDbEMsTUFBTTZELGVBQWNDLE9BQStCO1FBQ2pELE1BQU1yRSxXQUFXLE1BQU1aLFVBQVVnQixLQUFLLGlCQUFpQmlFO1FBQ3ZELE9BQU9yRSxTQUFTTztJQUNsQjtJQUVBLDhCQUE4QjtJQUM5QixNQUFNK0QsZUFBY0gsU0FBaUIsRUFBRUUsT0FBK0I7UUFDcEUsTUFBTXJFLFdBQVcsTUFBTVosVUFBVTZDLElBQUksQ0FBQyxjQUFjLEVBQUVrQyxVQUFVLENBQUMsRUFBRUU7UUFDbkUsT0FBT3JFLFNBQVNPO0lBQ2xCO0lBRUEsOEJBQThCO0lBQzlCLE1BQU1nRSxlQUFjSixTQUFpQjtRQUNuQyxNQUFNL0UsVUFBVWdELE9BQU8sQ0FBQyxjQUFjLEVBQUUrQixVQUFVLENBQUM7SUFDckQ7SUFFQSx5QkFBeUI7SUFDekIsTUFBTUssaUJBQWdCTCxTQUFpQjtRQUNyQyxpRUFBaUU7UUFDakUsTUFBTSxDQUFDTSxVQUFVQyxVQUFVLEdBQUcsTUFBTTVELFFBQVF1QyxJQUFJO1lBQzlDckMsVUFBVUMsWUFBWTtnQkFBRU0sWUFBWTRDO1lBQVU7WUFDOUMzQixZQUFZQyxhQUFhO2dCQUFFbEIsWUFBWTRDO1lBQVU7U0FDbEQ7UUFFRCxPQUFPO1lBQ0xRLGdCQUFnQkYsU0FBU0c7WUFDekJDLGlCQUFpQkgsVUFBVUU7WUFDM0JFLGlCQUFpQkwsU0FBU00sT0FBT0MsQ0FBQUEsSUFBS0EsRUFBRUMsa0JBQWtCLElBQUlDLEtBQUtGLEVBQUVDLG1CQUFtQixJQUFJQyxRQUFRTjtZQUNwR08saUJBQWlCQyxLQUFLQyxJQUFJWixTQUFTRyxRQUFRRixVQUFVRSxRQUFRLHlCQUF5QjtRQUN4RjtJQUNGO0FBQ0YsRUFBRTtBQUVGLGdCQUFnQjtBQUNULE1BQU1VLGVBQWU7SUFDMUIsMkJBQTJCO0lBQzNCLE1BQU1DO1FBQ0osNkZBQTZGO1FBQzdGLE1BQU0sQ0FBQ2QsVUFBVUMsV0FBV2MsU0FBUyxHQUFHLE1BQU0xRSxRQUFRdUMsSUFBSTtZQUN4RHJDLFVBQVVDO1lBQ1Z1QixZQUFZQztZQUNabUIsV0FBV0M7U0FDWjtRQUVELHVCQUF1QjtRQUN2QixNQUFNNEIsaUJBQWlCaEIsU0FBU2lCLE9BQU8sQ0FBQ0MsS0FBSzdEO1lBQzNDLE1BQU04RCxPQUFPOUQsT0FBT1QsZUFBZTtZQUNuQ3NFLEdBQUcsQ0FBQ0MsS0FBSyxHQUFHLENBQUNELEdBQUcsQ0FBQ0MsS0FBSyxJQUFJLEtBQUs7WUFDL0IsT0FBT0Q7UUFDVCxHQUFHLENBQUM7UUFFSixNQUFNRSxvQkFBb0JwQixTQUFTaUIsT0FBTyxDQUFDQyxLQUFLN0Q7WUFDOUMsSUFBSUEsT0FBT1AsWUFBWTtnQkFDckIsTUFBTThDLFVBQVVtQixTQUFTTSxLQUFLQyxDQUFBQSxJQUFLQSxFQUFFeEMsT0FBT3pCLE9BQU9QO2dCQUNuRCxNQUFNeUUsY0FBYzNCLFNBQVM0QixRQUFRO2dCQUNyQ04sR0FBRyxDQUFDSyxZQUFZLEdBQUcsQ0FBQ0wsR0FBRyxDQUFDSyxZQUFZLElBQUksS0FBSztZQUMvQztZQUNBLE9BQU9MO1FBQ1QsR0FBRyxDQUFDO1FBRUosdURBQXVEO1FBQ3ZELE1BQU1PLGlCQUFpQjtlQUNsQnpCLFNBQVMwQixNQUFNLEdBQUcsR0FBRzdDLElBQUl4QixDQUFBQSxTQUFXO29CQUNyQ3lCLElBQUl6QixPQUFPeUI7b0JBQ1hxQyxNQUFNO29CQUNOUSxPQUFPLENBQUMsZ0JBQWdCLEVBQUV0RSxPQUFPdUUsWUFBWSxDQUFDO29CQUM5Q0MsYUFBYSxDQUFDLEVBQUV4RSxPQUFPVCxlQUFlLFlBQVksbUJBQW1CLENBQUM7b0JBQ3RFa0YsV0FBV3pFLE9BQU8wRTtvQkFDbEJDLFdBQVczRSxPQUFPeUI7Z0JBQ3BCO2VBQ0dtQixVQUFVeUIsTUFBTSxHQUFHLEdBQUc3QyxJQUFJb0QsQ0FBQUEsTUFBUTtvQkFDbkNuRCxJQUFJbUQsSUFBSW5EO29CQUNScUMsTUFBTTtvQkFDTlEsT0FBTyxDQUFDLG1CQUFtQixFQUFFTSxJQUFJQyxrQkFBa0IsQ0FBQztvQkFDcERMLGFBQWEsQ0FBQyw0QkFBNEIsRUFBRUksSUFBSWhFLGtCQUFrQixDQUFDO29CQUNuRTZELFdBQVdHLElBQUlGO29CQUNmSSxhQUFhRixJQUFJbkQ7Z0JBQ25CO1NBQ0QsQ0FBQ3NELEtBQUssQ0FBQ0MsR0FBR0MsSUFBTSxJQUFJN0IsS0FBSzZCLEVBQUVSLFdBQVdTLFlBQVksSUFBSTlCLEtBQUs0QixFQUFFUCxXQUFXUyxXQUFXYixNQUFNLEdBQUc7UUFFN0YsT0FBTztZQUNMeEIsZ0JBQWdCRixTQUFTRztZQUN6QkMsaUJBQWlCSCxVQUFVRTtZQUMzQnFDLGtCQUFrQnhCO1lBQ2xCeUIscUJBQXFCckI7WUFDckJWLGlCQUFpQmU7WUFDakJpQixtQkFBbUI7Z0JBQ2pCQyxPQUFPO2dCQUNQQyxhQUFhLENBQUM7WUFDaEI7UUFDRjtJQUNGO0FBQ0YsRUFBRTtBQUVGLGlFQUFlakksU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3VzLWluc3VyYW5jZS1kZXRhaWxzLWZyb250ZW5kLy4vc3JjL3NlcnZpY2VzL2FwaVNlcnZpY2UudHM/MjY5OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnO1xuaW1wb3J0IHsgXG4gIEluc3VyYW5jZVBvbGljeSwgXG4gIEluc3VyYW5jZVBvbGljeUNyZWF0ZSwgXG4gIEluc3VyYW5jZUNhcnJpZXIsIFxuICBQb2xpY3lEb2N1bWVudCwgXG4gIFBvbGljeURvY3VtZW50V2l0aFRleHQsXG4gIENvdmVyYWdlQmVuZWZpdCxcbiAgUmVkRmxhZyxcbiAgRGFzaGJvYXJkU3RhdHMsXG4gIFBhZ2luYXRlZFJlc3BvbnNlLFxuICBQb2xpY3lGaWx0ZXJzLFxuICBEb2N1bWVudEZpbHRlcnNcbn0gZnJvbSAnLi4vdHlwZXMvYXBpJztcblxuY29uc3QgQVBJX0JBU0VfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAwJztcblxuLy8gQ3JlYXRlIGF4aW9zIGluc3RhbmNlIHdpdGggaW50ZXJjZXB0b3JzIGZvciBhdXRoXG5jb25zdCBhcGlDbGllbnQgPSBheGlvcy5jcmVhdGUoe1xuICBiYXNlVVJMOiBBUElfQkFTRV9VUkwsXG59KTtcblxuLy8gQWRkIGF1dGggdG9rZW4gdG8gcmVxdWVzdHNcbmFwaUNsaWVudC5pbnRlcmNlcHRvcnMucmVxdWVzdC51c2UoKGNvbmZpZykgPT4ge1xuICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhY2Nlc3NfdG9rZW4nKTtcbiAgaWYgKHRva2VuKSB7XG4gICAgY29uZmlnLmhlYWRlcnMuQXV0aG9yaXphdGlvbiA9IGBCZWFyZXIgJHt0b2tlbn1gO1xuICB9XG4gIHJldHVybiBjb25maWc7XG59KTtcblxuLy8gSGFuZGxlIHRva2VuIHJlZnJlc2ggb24gNDAxXG5hcGlDbGllbnQuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcbiAgKHJlc3BvbnNlKSA9PiByZXNwb25zZSxcbiAgYXN5bmMgKGVycm9yKSA9PiB7XG4gICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgLy8gVHJ5IHRvIHJlZnJlc2ggdG9rZW5cbiAgICAgIGNvbnN0IHJlZnJlc2hUb2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdyZWZyZXNoX3Rva2VuJyk7XG4gICAgICBpZiAocmVmcmVzaFRva2VuKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KGAke0FQSV9CQVNFX1VSTH0vYXBpL2F1dGgvcmVmcmVzaC10b2tlbmAsIHtcbiAgICAgICAgICAgIHJlZnJlc2hfdG9rZW46IHJlZnJlc2hUb2tlblxuICAgICAgICAgIH0pO1xuICAgICAgICAgIGNvbnN0IHsgYWNjZXNzX3Rva2VuIH0gPSByZXNwb25zZS5kYXRhO1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhY2Nlc3NfdG9rZW4nLCBhY2Nlc3NfdG9rZW4pO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIFJldHJ5IG9yaWdpbmFsIHJlcXVlc3RcbiAgICAgICAgICBlcnJvci5jb25maWcuaGVhZGVycy5BdXRob3JpemF0aW9uID0gYEJlYXJlciAke2FjY2Vzc190b2tlbn1gO1xuICAgICAgICAgIHJldHVybiBhcGlDbGllbnQucmVxdWVzdChlcnJvci5jb25maWcpO1xuICAgICAgICB9IGNhdGNoIChyZWZyZXNoRXJyb3IpIHtcbiAgICAgICAgICAvLyBSZWZyZXNoIGZhaWxlZCwgcmVkaXJlY3QgdG8gbG9naW5cbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYWNjZXNzX3Rva2VuJyk7XG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3JlZnJlc2hfdG9rZW4nKTtcbiAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBObyByZWZyZXNoIHRva2VuLCByZWRpcmVjdCB0byBsb2dpblxuICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpO1xuICB9XG4pO1xuXG4vLyBQb2xpY3kgQVBJXG5leHBvcnQgY29uc3QgcG9saWN5QXBpID0ge1xuICAvLyBHZXQgYWxsIHBvbGljaWVzIGZvciBjdXJyZW50IHVzZXJcbiAgYXN5bmMgZ2V0UG9saWNpZXMoZmlsdGVycz86IFBvbGljeUZpbHRlcnMpOiBQcm9taXNlPEluc3VyYW5jZVBvbGljeVtdPiB7XG4gICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgIGlmIChmaWx0ZXJzPy5wb2xpY3lfdHlwZSkgcGFyYW1zLmFwcGVuZCgncG9saWN5X3R5cGUnLCBmaWx0ZXJzLnBvbGljeV90eXBlKTtcbiAgICBpZiAoZmlsdGVycz8uY2Fycmllcl9pZCkgcGFyYW1zLmFwcGVuZCgnY2Fycmllcl9pZCcsIGZpbHRlcnMuY2Fycmllcl9pZCk7XG4gICAgaWYgKGZpbHRlcnM/LnNlYXJjaCkgcGFyYW1zLmFwcGVuZCgnc2VhcmNoJywgZmlsdGVycy5zZWFyY2gpO1xuICAgIFxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChgL2FwaS9wb2xpY2llcz8ke3BhcmFtcy50b1N0cmluZygpfWApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9LFxuXG4gIC8vIEdldCBzaW5nbGUgcG9saWN5IGJ5IElEXG4gIGFzeW5jIGdldFBvbGljeShwb2xpY3lJZDogc3RyaW5nKTogUHJvbWlzZTxJbnN1cmFuY2VQb2xpY3k+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoYC9hcGkvcG9saWNpZXMvJHtwb2xpY3lJZH1gKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICAvLyBDcmVhdGUgbmV3IHBvbGljeVxuICBhc3luYyBjcmVhdGVQb2xpY3kocG9saWN5OiBJbnN1cmFuY2VQb2xpY3lDcmVhdGUpOiBQcm9taXNlPEluc3VyYW5jZVBvbGljeT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoJy9hcGkvcG9saWNpZXMnLCBwb2xpY3kpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9LFxuXG4gIC8vIFVwZGF0ZSBwb2xpY3lcbiAgYXN5bmMgdXBkYXRlUG9saWN5KHBvbGljeUlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8SW5zdXJhbmNlUG9saWN5Q3JlYXRlPik6IFByb21pc2U8SW5zdXJhbmNlUG9saWN5PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucHV0KGAvYXBpL3BvbGljaWVzLyR7cG9saWN5SWR9YCwgdXBkYXRlcyk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH0sXG5cbiAgLy8gVXBkYXRlIHBvbGljeVxuICBhc3luYyB1cGRhdGVQb2xpY3kocG9saWN5SWQ6IHN0cmluZywgcG9saWN5RGF0YTogYW55KTogUHJvbWlzZTxJbnN1cmFuY2VQb2xpY3k+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wdXQoYC9hcGkvcG9saWNpZXMvJHtwb2xpY3lJZH1gLCBwb2xpY3lEYXRhKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICAvLyBEZWxldGUgcG9saWN5XG4gIGFzeW5jIGRlbGV0ZVBvbGljeShwb2xpY3lJZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgYXdhaXQgYXBpQ2xpZW50LmRlbGV0ZShgL2FwaS9wb2xpY2llcy8ke3BvbGljeUlkfWApO1xuICB9LFxuXG4gIC8vIEdldCBiZW5lZml0cyBmb3IgcG9saWN5XG4gIGFzeW5jIGdldFBvbGljeUJlbmVmaXRzKHBvbGljeUlkOiBzdHJpbmcpOiBQcm9taXNlPENvdmVyYWdlQmVuZWZpdFtdPiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KGAvYXBpL3BvbGljaWVzLyR7cG9saWN5SWR9L2JlbmVmaXRzYCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH0sXG5cbiAgLy8gR2V0IHJlZCBmbGFncyBmb3IgcG9saWN5XG4gIGFzeW5jIGdldFJlZEZsYWdzKHBvbGljeUlkOiBzdHJpbmcpOiBQcm9taXNlPFJlZEZsYWdbXT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChgL2FwaS9wb2xpY2llcy8ke3BvbGljeUlkfS9yZWQtZmxhZ3NgKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5yZWRfZmxhZ3MgfHwgW107XG4gIH1cbn07XG5cbi8vIERvY3VtZW50IEFQSVxuZXhwb3J0IGNvbnN0IGRvY3VtZW50QXBpID0ge1xuICAvLyBHZXQgYWxsIGRvY3VtZW50cyBmb3IgY3VycmVudCB1c2VyXG4gIGFzeW5jIGdldERvY3VtZW50cyhmaWx0ZXJzPzogRG9jdW1lbnRGaWx0ZXJzKTogUHJvbWlzZTxQb2xpY3lEb2N1bWVudFtdPiB7XG4gICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgIGlmIChmaWx0ZXJzPy5wcm9jZXNzaW5nX3N0YXR1cykgcGFyYW1zLmFwcGVuZCgncHJvY2Vzc2luZ19zdGF0dXMnLCBmaWx0ZXJzLnByb2Nlc3Npbmdfc3RhdHVzKTtcbiAgICBpZiAoZmlsdGVycz8uY2Fycmllcl9pZCkgcGFyYW1zLmFwcGVuZCgnY2Fycmllcl9pZCcsIGZpbHRlcnMuY2Fycmllcl9pZCk7XG4gICAgaWYgKGZpbHRlcnM/LnNlYXJjaCkgcGFyYW1zLmFwcGVuZCgnc2VhcmNoJywgZmlsdGVycy5zZWFyY2gpO1xuICAgIFxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChgL2FwaS9kb2N1bWVudHM/JHtwYXJhbXMudG9TdHJpbmcoKX1gKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICAvLyBHZXQgc2luZ2xlIGRvY3VtZW50IGJ5IElEXG4gIGFzeW5jIGdldERvY3VtZW50KGRvY3VtZW50SWQ6IHN0cmluZyk6IFByb21pc2U8UG9saWN5RG9jdW1lbnRXaXRoVGV4dD4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChgL2FwaS9kb2N1bWVudHMvJHtkb2N1bWVudElkfWApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9LFxuXG4gIC8vIFVwbG9hZCBuZXcgZG9jdW1lbnRcbiAgYXN5bmMgdXBsb2FkRG9jdW1lbnQoZmlsZTogRmlsZSwgY2FycmllcklEPzogc3RyaW5nKTogUHJvbWlzZTxQb2xpY3lEb2N1bWVudD4ge1xuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG4gICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSk7XG4gICAgaWYgKGNhcnJpZXJJRCkge1xuICAgICAgZm9ybURhdGEuYXBwZW5kKCdjYXJyaWVyX2lkJywgY2FycmllcklEKTtcbiAgICB9XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KCcvYXBpL2RvY3VtZW50cy91cGxvYWQnLCBmb3JtRGF0YSwge1xuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ211bHRpcGFydC9mb3JtLWRhdGEnLFxuICAgICAgfSxcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICAvLyBEZWxldGUgZG9jdW1lbnRcbiAgYXN5bmMgZGVsZXRlRG9jdW1lbnQoZG9jdW1lbnRJZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgYXdhaXQgYXBpQ2xpZW50LmRlbGV0ZShgL2FwaS9kb2N1bWVudHMvJHtkb2N1bWVudElkfWApO1xuICB9LFxuXG4gIC8vIEJ1bGsgZGVsZXRlIGRvY3VtZW50c1xuICBhc3luYyBidWxrRGVsZXRlRG9jdW1lbnRzKGRvY3VtZW50SWRzOiBzdHJpbmdbXSk6IFByb21pc2U8dm9pZD4ge1xuICAgIC8vIFNpbmNlIHRoZXJlJ3Mgbm8gYnVsayBkZWxldGUgZW5kcG9pbnQsIGRlbGV0ZSBvbmUgYnkgb25lXG4gICAgYXdhaXQgUHJvbWlzZS5hbGwoZG9jdW1lbnRJZHMubWFwKGlkID0+IHRoaXMuZGVsZXRlRG9jdW1lbnQoaWQpKSk7XG4gIH0sXG5cbiAgLy8gR2V0IGRvY3VtZW50IHByb2Nlc3Npbmcgc3RhdHVzIChmb3IgcG9sbGluZylcbiAgYXN5bmMgZ2V0RG9jdW1lbnRTdGF0dXMoZG9jdW1lbnRJZDogc3RyaW5nKTogUHJvbWlzZTx7IHByb2Nlc3Npbmdfc3RhdHVzOiBzdHJpbmc7IHByb2Nlc3NpbmdfZXJyb3I/OiBzdHJpbmcgfT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChgL2FwaS9kb2N1bWVudHMvJHtkb2N1bWVudElkfWApO1xuICAgIHJldHVybiB7XG4gICAgICBwcm9jZXNzaW5nX3N0YXR1czogcmVzcG9uc2UuZGF0YS5wcm9jZXNzaW5nX3N0YXR1cyxcbiAgICAgIHByb2Nlc3NpbmdfZXJyb3I6IHJlc3BvbnNlLmRhdGEucHJvY2Vzc2luZ19lcnJvclxuICAgIH07XG4gIH0sXG5cbiAgLy8gRG93bmxvYWQgZG9jdW1lbnQgKGlmIGJhY2tlbmQgc3VwcG9ydHMgaXQpXG4gIGFzeW5jIGRvd25sb2FkRG9jdW1lbnQoZG9jdW1lbnRJZDogc3RyaW5nKTogUHJvbWlzZTxCbG9iPiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KGAvYXBpL2RvY3VtZW50cy8ke2RvY3VtZW50SWR9L2Rvd25sb2FkYCwge1xuICAgICAgcmVzcG9uc2VUeXBlOiAnYmxvYidcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxufTtcblxuLy8gQ2FycmllciBBUElcbmV4cG9ydCBjb25zdCBjYXJyaWVyQXBpID0ge1xuICAvLyBHZXQgYWxsIGNhcnJpZXJzIHdpdGggb3B0aW9uYWwgZmlsdGVyaW5nXG4gIGFzeW5jIGdldENhcnJpZXJzKGZpbHRlcnM/OiBDYXJyaWVyRmlsdGVycyk6IFByb21pc2U8SW5zdXJhbmNlQ2FycmllcltdPiB7XG4gICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgIGlmIChmaWx0ZXJzPy5zZWFyY2gpIHBhcmFtcy5hcHBlbmQoJ3NlYXJjaCcsIGZpbHRlcnMuc2VhcmNoKTtcbiAgICBpZiAoZmlsdGVycz8uaXNfYWN0aXZlICE9PSB1bmRlZmluZWQpIHBhcmFtcy5hcHBlbmQoJ2lzX2FjdGl2ZScsIGZpbHRlcnMuaXNfYWN0aXZlLnRvU3RyaW5nKCkpO1xuICAgIGlmIChmaWx0ZXJzPy5za2lwKSBwYXJhbXMuYXBwZW5kKCdza2lwJywgZmlsdGVycy5za2lwLnRvU3RyaW5nKCkpO1xuICAgIGlmIChmaWx0ZXJzPy5saW1pdCkgcGFyYW1zLmFwcGVuZCgnbGltaXQnLCBmaWx0ZXJzLmxpbWl0LnRvU3RyaW5nKCkpO1xuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KGAvYXBpL2NhcnJpZXJzPyR7cGFyYW1zLnRvU3RyaW5nKCl9YCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH0sXG5cbiAgLy8gR2V0IHNpbmdsZSBjYXJyaWVyIGJ5IElEXG4gIGFzeW5jIGdldENhcnJpZXIoY2FycmllcklkOiBzdHJpbmcpOiBQcm9taXNlPEluc3VyYW5jZUNhcnJpZXI+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoYC9hcGkvY2FycmllcnMvJHtjYXJyaWVySWR9YCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH0sXG5cbiAgLy8gQ3JlYXRlIG5ldyBjYXJyaWVyIChhZG1pbiBvbmx5KVxuICBhc3luYyBjcmVhdGVDYXJyaWVyKGNhcnJpZXI6IEluc3VyYW5jZUNhcnJpZXJDcmVhdGUpOiBQcm9taXNlPEluc3VyYW5jZUNhcnJpZXI+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KCcvYXBpL2NhcnJpZXJzJywgY2Fycmllcik7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH0sXG5cbiAgLy8gVXBkYXRlIGNhcnJpZXIgKGFkbWluIG9ubHkpXG4gIGFzeW5jIHVwZGF0ZUNhcnJpZXIoY2FycmllcklkOiBzdHJpbmcsIGNhcnJpZXI6IEluc3VyYW5jZUNhcnJpZXJVcGRhdGUpOiBQcm9taXNlPEluc3VyYW5jZUNhcnJpZXI+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wdXQoYC9hcGkvY2FycmllcnMvJHtjYXJyaWVySWR9YCwgY2Fycmllcik7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH0sXG5cbiAgLy8gRGVsZXRlIGNhcnJpZXIgKGFkbWluIG9ubHkpXG4gIGFzeW5jIGRlbGV0ZUNhcnJpZXIoY2FycmllcklkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBhd2FpdCBhcGlDbGllbnQuZGVsZXRlKGAvYXBpL2NhcnJpZXJzLyR7Y2FycmllcklkfWApO1xuICB9LFxuXG4gIC8vIEdldCBjYXJyaWVyIHN0YXRpc3RpY3NcbiAgYXN5bmMgZ2V0Q2FycmllclN0YXRzKGNhcnJpZXJJZDogc3RyaW5nKTogUHJvbWlzZTxDYXJyaWVyU3RhdHM+IHtcbiAgICAvLyBTaW5jZSB0aGVyZSdzIG5vIHNwZWNpZmljIHN0YXRzIGVuZHBvaW50LCB3ZSdsbCBhZ2dyZWdhdGUgZGF0YVxuICAgIGNvbnN0IFtwb2xpY2llcywgZG9jdW1lbnRzXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgIHBvbGljeUFwaS5nZXRQb2xpY2llcyh7IGNhcnJpZXJfaWQ6IGNhcnJpZXJJZCB9KSxcbiAgICAgIGRvY3VtZW50QXBpLmdldERvY3VtZW50cyh7IGNhcnJpZXJfaWQ6IGNhcnJpZXJJZCB9KVxuICAgIF0pO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIHRvdGFsX3BvbGljaWVzOiBwb2xpY2llcy5sZW5ndGgsXG4gICAgICB0b3RhbF9kb2N1bWVudHM6IGRvY3VtZW50cy5sZW5ndGgsXG4gICAgICBhY3RpdmVfcG9saWNpZXM6IHBvbGljaWVzLmZpbHRlcihwID0+IHAuZWZmZWN0aXZlX2RhdGUgJiYgbmV3IERhdGUocC5lZmZlY3RpdmVfZGF0ZSkgPD0gbmV3IERhdGUoKSkubGVuZ3RoLFxuICAgICAgcmVjZW50X2FjdGl2aXR5OiBNYXRoLm1heChwb2xpY2llcy5sZW5ndGgsIGRvY3VtZW50cy5sZW5ndGgpIC8vIFNpbXBsZSBhY3Rpdml0eSBtZXRyaWNcbiAgICB9O1xuICB9XG59O1xuXG4vLyBEYXNoYm9hcmQgQVBJXG5leHBvcnQgY29uc3QgZGFzaGJvYXJkQXBpID0ge1xuICAvLyBHZXQgZGFzaGJvYXJkIHN0YXRpc3RpY3NcbiAgYXN5bmMgZ2V0RGFzaGJvYXJkU3RhdHMoKTogUHJvbWlzZTxEYXNoYm9hcmRTdGF0cz4ge1xuICAgIC8vIFNpbmNlIHRoZXJlJ3Mgbm8gc3BlY2lmaWMgZGFzaGJvYXJkIGVuZHBvaW50LCB3ZSdsbCBhZ2dyZWdhdGUgZGF0YSBmcm9tIG11bHRpcGxlIGVuZHBvaW50c1xuICAgIGNvbnN0IFtwb2xpY2llcywgZG9jdW1lbnRzLCBjYXJyaWVyc10gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICBwb2xpY3lBcGkuZ2V0UG9saWNpZXMoKSxcbiAgICAgIGRvY3VtZW50QXBpLmdldERvY3VtZW50cygpLFxuICAgICAgY2FycmllckFwaS5nZXRDYXJyaWVycygpXG4gICAgXSk7XG5cbiAgICAvLyBDYWxjdWxhdGUgc3RhdGlzdGljc1xuICAgIGNvbnN0IHBvbGljaWVzQnlUeXBlID0gcG9saWNpZXMucmVkdWNlKChhY2MsIHBvbGljeSkgPT4ge1xuICAgICAgY29uc3QgdHlwZSA9IHBvbGljeS5wb2xpY3lfdHlwZSB8fCAndW5rbm93bic7XG4gICAgICBhY2NbdHlwZV0gPSAoYWNjW3R5cGVdIHx8IDApICsgMTtcbiAgICAgIHJldHVybiBhY2M7XG4gICAgfSwge30gYXMgUmVjb3JkPHN0cmluZywgbnVtYmVyPik7XG5cbiAgICBjb25zdCBwb2xpY2llc0J5Q2FycmllciA9IHBvbGljaWVzLnJlZHVjZSgoYWNjLCBwb2xpY3kpID0+IHtcbiAgICAgIGlmIChwb2xpY3kuY2Fycmllcl9pZCkge1xuICAgICAgICBjb25zdCBjYXJyaWVyID0gY2FycmllcnMuZmluZChjID0+IGMuaWQgPT09IHBvbGljeS5jYXJyaWVyX2lkKTtcbiAgICAgICAgY29uc3QgY2Fycmllck5hbWUgPSBjYXJyaWVyPy5uYW1lIHx8ICdVbmtub3duJztcbiAgICAgICAgYWNjW2NhcnJpZXJOYW1lXSA9IChhY2NbY2Fycmllck5hbWVdIHx8IDApICsgMTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBhY2M7XG4gICAgfSwge30gYXMgUmVjb3JkPHN0cmluZywgbnVtYmVyPik7XG5cbiAgICAvLyBHZW5lcmF0ZSByZWNlbnQgYWN0aXZpdHkgZnJvbSBwb2xpY2llcyBhbmQgZG9jdW1lbnRzXG4gICAgY29uc3QgcmVjZW50QWN0aXZpdHkgPSBbXG4gICAgICAuLi5wb2xpY2llcy5zbGljZSgwLCAzKS5tYXAocG9saWN5ID0+ICh7XG4gICAgICAgIGlkOiBwb2xpY3kuaWQsXG4gICAgICAgIHR5cGU6ICdwb2xpY3lfY3JlYXRlZCcgYXMgY29uc3QsXG4gICAgICAgIHRpdGxlOiBgUG9saWN5IENyZWF0ZWQ6ICR7cG9saWN5LnBvbGljeV9uYW1lfWAsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBgJHtwb2xpY3kucG9saWN5X3R5cGUgfHwgJ0luc3VyYW5jZSd9IHBvbGljeSB3YXMgY3JlYXRlZGAsXG4gICAgICAgIHRpbWVzdGFtcDogcG9saWN5LmNyZWF0ZWRfYXQsXG4gICAgICAgIHBvbGljeV9pZDogcG9saWN5LmlkXG4gICAgICB9KSksXG4gICAgICAuLi5kb2N1bWVudHMuc2xpY2UoMCwgMykubWFwKGRvYyA9PiAoe1xuICAgICAgICBpZDogZG9jLmlkLFxuICAgICAgICB0eXBlOiAnZG9jdW1lbnRfdXBsb2FkZWQnIGFzIGNvbnN0LFxuICAgICAgICB0aXRsZTogYERvY3VtZW50IFVwbG9hZGVkOiAke2RvYy5vcmlnaW5hbF9maWxlbmFtZX1gLFxuICAgICAgICBkZXNjcmlwdGlvbjogYERvY3VtZW50IHByb2Nlc3Npbmcgc3RhdHVzOiAke2RvYy5wcm9jZXNzaW5nX3N0YXR1c31gLFxuICAgICAgICB0aW1lc3RhbXA6IGRvYy5jcmVhdGVkX2F0LFxuICAgICAgICBkb2N1bWVudF9pZDogZG9jLmlkXG4gICAgICB9KSlcbiAgICBdLnNvcnQoKGEsIGIpID0+IG5ldyBEYXRlKGIudGltZXN0YW1wKS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShhLnRpbWVzdGFtcCkuZ2V0VGltZSgpKS5zbGljZSgwLCA1KTtcblxuICAgIHJldHVybiB7XG4gICAgICB0b3RhbF9wb2xpY2llczogcG9saWNpZXMubGVuZ3RoLFxuICAgICAgdG90YWxfZG9jdW1lbnRzOiBkb2N1bWVudHMubGVuZ3RoLFxuICAgICAgcG9saWNpZXNfYnlfdHlwZTogcG9saWNpZXNCeVR5cGUsXG4gICAgICBwb2xpY2llc19ieV9jYXJyaWVyOiBwb2xpY2llc0J5Q2FycmllcixcbiAgICAgIHJlY2VudF9hY3Rpdml0eTogcmVjZW50QWN0aXZpdHksXG4gICAgICByZWRfZmxhZ3Nfc3VtbWFyeToge1xuICAgICAgICB0b3RhbDogMCwgLy8gV2lsbCBiZSBwb3B1bGF0ZWQgd2hlbiB3ZSBmZXRjaCByZWQgZmxhZ3NcbiAgICAgICAgYnlfc2V2ZXJpdHk6IHt9XG4gICAgICB9XG4gICAgfTtcbiAgfVxufTtcblxuZXhwb3J0IGRlZmF1bHQgYXBpQ2xpZW50O1xuIl0sIm5hbWVzIjpbImF4aW9zIiwiQVBJX0JBU0VfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJhcGlDbGllbnQiLCJjcmVhdGUiLCJiYXNlVVJMIiwiaW50ZXJjZXB0b3JzIiwicmVxdWVzdCIsInVzZSIsImNvbmZpZyIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImhlYWRlcnMiLCJBdXRob3JpemF0aW9uIiwicmVzcG9uc2UiLCJlcnJvciIsInN0YXR1cyIsInJlZnJlc2hUb2tlbiIsInBvc3QiLCJyZWZyZXNoX3Rva2VuIiwiYWNjZXNzX3Rva2VuIiwiZGF0YSIsInNldEl0ZW0iLCJyZWZyZXNoRXJyb3IiLCJyZW1vdmVJdGVtIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiUHJvbWlzZSIsInJlamVjdCIsInBvbGljeUFwaSIsImdldFBvbGljaWVzIiwiZmlsdGVycyIsInBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsInBvbGljeV90eXBlIiwiYXBwZW5kIiwiY2Fycmllcl9pZCIsInNlYXJjaCIsImdldCIsInRvU3RyaW5nIiwiZ2V0UG9saWN5IiwicG9saWN5SWQiLCJjcmVhdGVQb2xpY3kiLCJwb2xpY3kiLCJ1cGRhdGVQb2xpY3kiLCJ1cGRhdGVzIiwicHV0IiwicG9saWN5RGF0YSIsImRlbGV0ZVBvbGljeSIsImRlbGV0ZSIsImdldFBvbGljeUJlbmVmaXRzIiwiZ2V0UmVkRmxhZ3MiLCJyZWRfZmxhZ3MiLCJkb2N1bWVudEFwaSIsImdldERvY3VtZW50cyIsInByb2Nlc3Npbmdfc3RhdHVzIiwiZ2V0RG9jdW1lbnQiLCJkb2N1bWVudElkIiwidXBsb2FkRG9jdW1lbnQiLCJmaWxlIiwiY2FycmllcklEIiwiZm9ybURhdGEiLCJGb3JtRGF0YSIsImRlbGV0ZURvY3VtZW50IiwiYnVsa0RlbGV0ZURvY3VtZW50cyIsImRvY3VtZW50SWRzIiwiYWxsIiwibWFwIiwiaWQiLCJnZXREb2N1bWVudFN0YXR1cyIsInByb2Nlc3NpbmdfZXJyb3IiLCJkb3dubG9hZERvY3VtZW50IiwicmVzcG9uc2VUeXBlIiwiY2FycmllckFwaSIsImdldENhcnJpZXJzIiwiaXNfYWN0aXZlIiwidW5kZWZpbmVkIiwic2tpcCIsImxpbWl0IiwiZ2V0Q2FycmllciIsImNhcnJpZXJJZCIsImNyZWF0ZUNhcnJpZXIiLCJjYXJyaWVyIiwidXBkYXRlQ2FycmllciIsImRlbGV0ZUNhcnJpZXIiLCJnZXRDYXJyaWVyU3RhdHMiLCJwb2xpY2llcyIsImRvY3VtZW50cyIsInRvdGFsX3BvbGljaWVzIiwibGVuZ3RoIiwidG90YWxfZG9jdW1lbnRzIiwiYWN0aXZlX3BvbGljaWVzIiwiZmlsdGVyIiwicCIsImVmZmVjdGl2ZV9kYXRlIiwiRGF0ZSIsInJlY2VudF9hY3Rpdml0eSIsIk1hdGgiLCJtYXgiLCJkYXNoYm9hcmRBcGkiLCJnZXREYXNoYm9hcmRTdGF0cyIsImNhcnJpZXJzIiwicG9saWNpZXNCeVR5cGUiLCJyZWR1Y2UiLCJhY2MiLCJ0eXBlIiwicG9saWNpZXNCeUNhcnJpZXIiLCJmaW5kIiwiYyIsImNhcnJpZXJOYW1lIiwibmFtZSIsInJlY2VudEFjdGl2aXR5Iiwic2xpY2UiLCJ0aXRsZSIsInBvbGljeV9uYW1lIiwiZGVzY3JpcHRpb24iLCJ0aW1lc3RhbXAiLCJjcmVhdGVkX2F0IiwicG9saWN5X2lkIiwiZG9jIiwib3JpZ2luYWxfZmlsZW5hbWUiLCJkb2N1bWVudF9pZCIsInNvcnQiLCJhIiwiYiIsImdldFRpbWUiLCJwb2xpY2llc19ieV90eXBlIiwicG9saWNpZXNfYnlfY2FycmllciIsInJlZF9mbGFnc19zdW1tYXJ5IiwidG90YWwiLCJieV9zZXZlcml0eSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/services/apiService.ts\n");

/***/ }),

/***/ "./src/services/authService.ts":
/*!*************************************!*\
  !*** ./src/services/authService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// API Configuration\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst TOKEN_KEY = \"auth_token\";\nconst REFRESH_TOKEN_KEY = \"refresh_token\";\n// Create axios instance with interceptors\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napiClient.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(TOKEN_KEY);\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle token refresh\napiClient.interceptors.response.use((response)=>response, async (error)=>{\n    const originalRequest = error.config;\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_BASE_URL}/api/auth/refresh-token`, {\n                    token: refreshToken\n                });\n                const { access_token, refresh_token } = response.data;\n                localStorage.setItem(TOKEN_KEY, access_token);\n                localStorage.setItem(REFRESH_TOKEN_KEY, refresh_token);\n                // Retry original request with new token\n                originalRequest.headers.Authorization = `Bearer ${access_token}`;\n                return apiClient(originalRequest);\n            }\n        } catch (refreshError) {\n            // Refresh failed, redirect to login\n            authService.logout();\n            window.location.href = \"/login\";\n        }\n    }\n    return Promise.reject(error);\n});\nconst authService = {\n    // Login user\n    async login (email, password) {\n        try {\n            // Create form data for OAuth2 compatibility\n            const formData = new FormData();\n            formData.append(\"username\", email);\n            formData.append(\"password\", password);\n            const response = await apiClient.post(\"/api/auth/login\", formData, {\n                headers: {\n                    \"Content-Type\": \"application/x-www-form-urlencoded\"\n                }\n            });\n            const { access_token, refresh_token, user } = response.data;\n            // Store tokens securely\n            localStorage.setItem(TOKEN_KEY, access_token);\n            localStorage.setItem(REFRESH_TOKEN_KEY, refresh_token);\n            return response.data;\n        } catch (error) {\n            throw new Error(error.response?.data?.detail || \"Login failed. Please check your credentials.\");\n        }\n    },\n    // Register new user\n    async register (userData) {\n        try {\n            const response = await apiClient.post(\"/api/auth/register\", userData);\n            return response.data;\n        } catch (error) {\n            throw new Error(error.response?.data?.detail || \"Registration failed. Please try again.\");\n        }\n    },\n    // Get current user (for auth check)\n    async getCurrentUser () {\n        try {\n            const response = await apiClient.get(\"/api/users/me\");\n            return response.data;\n        } catch (error) {\n            throw new Error(\"Failed to get user information\");\n        }\n    },\n    // Logout user\n    logout () {\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(REFRESH_TOKEN_KEY);\n    },\n    // Get stored token\n    getToken () {\n        return localStorage.getItem(TOKEN_KEY);\n    },\n    // Remove stored token\n    removeToken () {\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(REFRESH_TOKEN_KEY);\n    },\n    // Check if user is authenticated\n    isAuthenticated () {\n        return !!this.getToken();\n    },\n    // Refresh access token\n    async refreshToken () {\n        try {\n            const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);\n            if (!refreshToken) {\n                throw new Error(\"No refresh token available\");\n            }\n            const response = await apiClient.post(\"/api/auth/refresh-token\", {\n                token: refreshToken\n            });\n            const { access_token, refresh_token } = response.data;\n            localStorage.setItem(TOKEN_KEY, access_token);\n            localStorage.setItem(REFRESH_TOKEN_KEY, refresh_token);\n            return response.data;\n        } catch (error) {\n            this.logout();\n            throw new Error(\"Token refresh failed\");\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authService);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/authService.ts\n");

/***/ }),

/***/ "./node_modules/next/dist/server/future/route-modules/pages/module.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/pages/module.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PagesRouteModule: function() {\n        return PagesRouteModule;\n    },\n    default: function() {\n        return _default;\n    }\n});\nconst _routemodule = __webpack_require__(/*! ../route-module */ \"../route-module\");\nconst _render = __webpack_require__(/*! ../../../render */ \"../../../render\");\nclass PagesRouteModule extends _routemodule.RouteModule {\n    setup() {\n        throw new Error(\"Method not implemented.\");\n    }\n    handle() {\n        throw new Error(\"Method not implemented.\");\n    }\n    async render(req, res, pathname, query, renderOpts) {\n        const result = await (0, _render.renderToHTML)(req, res, pathname, query, renderOpts);\n        return result;\n    }\n}\nconst _default = PagesRouteModule;\n\n//# sourceMappingURL=module.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/future/route-modules/pages/module.js\n");

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL3VzLWluc3VyYW5jZS1kZXRhaWxzLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL25leHQvbGluay5qcz83NWIzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2NsaWVudC9saW5rJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n");

/***/ }),

/***/ "./node_modules/react/cjs/react-jsx-dev-runtime.development.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! react */ \"react\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV$1 =  jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV$1;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/react/cjs/react-jsx-dev-runtime.development.js\n");

/***/ }),

/***/ "./node_modules/react/jsx-dev-runtime.js":
/*!***********************************************!*\
  !*** ./node_modules/react/jsx-dev-runtime.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"./node_modules/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx1SkFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly91cy1pbnN1cmFuY2UtZGV0YWlscy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/NTc3NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/react/jsx-dev-runtime.js\n");

/***/ }),

/***/ "../route-module":
/*!************************************************************************!*\
  !*** external "next/dist/server/future/route-modules/route-module.js" ***!
  \************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/future/route-modules/route-module.js");

/***/ }),

/***/ "../../../render":
/*!*********************************************!*\
  !*** external "next/dist/server/render.js" ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/render.js");

/***/ }),

/***/ "../shared/lib/app-router-context":
/*!*************************************************************!*\
  !*** external "next/dist/shared/lib/app-router-context.js" ***!
  \*************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/app-router-context.js");

/***/ }),

/***/ "../shared/lib/router-context":
/*!*********************************************************!*\
  !*** external "next/dist/shared/lib/router-context.js" ***!
  \*********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router-context.js");

/***/ }),

/***/ "../shared/lib/router/utils/add-path-prefix":
/*!***********************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/add-path-prefix.js" ***!
  \***********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix.js");

/***/ }),

/***/ "../shared/lib/router/utils/format-url":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/format-url.js" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-url.js");

/***/ }),

/***/ "../shared/lib/router/utils/is-local-url":
/*!********************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-local-url.js" ***!
  \********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-local-url.js");

/***/ }),

/***/ "../shared/lib/router/utils/parse-path":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/parse-path.js" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/parse-path.js");

/***/ }),

/***/ "../shared/lib/router/utils/remove-trailing-slash":
/*!*****************************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/remove-trailing-slash.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash.js");

/***/ }),

/***/ "../shared/lib/router/utils/resolve-href":
/*!********************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/resolve-href.js" ***!
  \********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/resolve-href.js");

/***/ }),

/***/ "../shared/lib/utils":
/*!************************************************!*\
  !*** external "next/dist/shared/lib/utils.js" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils.js");

/***/ }),

/***/ "next/router":
/*!******************************!*\
  !*** external "next/router" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/router");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "./node_modules/@swc/helpers/cjs/_interop_require_default.cjs":
/*!********************************************************************!*\
  !*** ./node_modules/@swc/helpers/cjs/_interop_require_default.cjs ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nexports._ = exports._interop_require_default = _interop_require_default;\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2Nqcy9faW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQuY2pzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFNBQVMsR0FBRyxnQ0FBZ0M7QUFDNUM7QUFDQSwyQ0FBMkM7QUFDM0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly91cy1pbnN1cmFuY2UtZGV0YWlscy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvY2pzL19pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5janM/OTk3YSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuZXhwb3J0cy5fID0gZXhwb3J0cy5faW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQgPSBfaW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQ7XG5mdW5jdGlvbiBfaW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQob2JqKSB7XG4gICAgcmV0dXJuIG9iaiAmJiBvYmouX19lc01vZHVsZSA/IG9iaiA6IHsgZGVmYXVsdDogb2JqIH07XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/cjs/_interop_require_default.cjs\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?page=%2Fcarriers&absolutePagePath=.%2Fsrc%5Cpages%5Ccarriers%5Cindex.tsx&preferredRegion=&middlewareConfig=e30%3D!"));
module.exports = __webpack_exports__;

})();