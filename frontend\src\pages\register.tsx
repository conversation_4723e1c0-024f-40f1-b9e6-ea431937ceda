import React from 'react';
import Head from 'next/head';
import { RegisterForm } from '../components/auth/RegisterForm';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';

const RegisterPage: React.FC = () => {
  return (
    <ProtectedRoute requireAuth={false}>
      <Head>
        <title>Create Account - US Insurance Platform</title>
        <meta name="description" content="Create your US Insurance Platform account" />
      </Head>
      <RegisterForm />
    </ProtectedRoute>
  );
};

export default RegisterPage;
