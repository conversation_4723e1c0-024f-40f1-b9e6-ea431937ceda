import axios from 'axios';
import { 
  InsurancePolicy, 
  InsurancePolicyCreate, 
  InsuranceCarrier, 
  PolicyDocument, 
  PolicyDocumentWithText,
  CoverageBenefit,
  RedFlag,
  DashboardStats,
  PaginatedResponse,
  PolicyFilters,
  DocumentFilters
} from '../types/api';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance with interceptors for auth
const apiClient = axios.create({
  baseURL: API_BASE_URL,
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token refresh on 401
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Try to refresh token
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/api/auth/refresh-token`, {
            refresh_token: refreshToken
          });
          const { access_token } = response.data;
          localStorage.setItem('access_token', access_token);
          
          // Retry original request
          error.config.headers.Authorization = `Bearer ${access_token}`;
          return apiClient.request(error.config);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          window.location.href = '/login';
        }
      } else {
        // No refresh token, redirect to login
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Policy API
export const policyApi = {
  // Get all policies for current user
  async getPolicies(filters?: PolicyFilters): Promise<InsurancePolicy[]> {
    const params = new URLSearchParams();
    if (filters?.policy_type) params.append('policy_type', filters.policy_type);
    if (filters?.carrier_id) params.append('carrier_id', filters.carrier_id);
    if (filters?.search) params.append('search', filters.search);
    
    const response = await apiClient.get(`/api/policies?${params.toString()}`);
    return response.data;
  },

  // Get single policy by ID
  async getPolicy(policyId: string): Promise<InsurancePolicy> {
    const response = await apiClient.get(`/api/policies/${policyId}`);
    return response.data;
  },

  // Create new policy
  async createPolicy(policy: InsurancePolicyCreate): Promise<InsurancePolicy> {
    const response = await apiClient.post('/api/policies', policy);
    return response.data;
  },

  // Update policy
  async updatePolicy(policyId: string, updates: Partial<InsurancePolicyCreate>): Promise<InsurancePolicy> {
    const response = await apiClient.put(`/api/policies/${policyId}`, updates);
    return response.data;
  },

  // Update policy
  async updatePolicy(policyId: string, policyData: any): Promise<InsurancePolicy> {
    const response = await apiClient.put(`/api/policies/${policyId}`, policyData);
    return response.data;
  },

  // Delete policy
  async deletePolicy(policyId: string): Promise<void> {
    await apiClient.delete(`/api/policies/${policyId}`);
  },

  // Get benefits for policy
  async getPolicyBenefits(policyId: string): Promise<CoverageBenefit[]> {
    const response = await apiClient.get(`/api/policies/${policyId}/benefits`);
    return response.data;
  },

  // Get red flags for policy
  async getRedFlags(policyId: string): Promise<RedFlag[]> {
    const response = await apiClient.get(`/api/policies/${policyId}/red-flags`);
    return response.data.red_flags || [];
  }
};

// Document API
export const documentApi = {
  // Get all documents for current user
  async getDocuments(filters?: DocumentFilters): Promise<PolicyDocument[]> {
    const params = new URLSearchParams();
    if (filters?.processing_status) params.append('processing_status', filters.processing_status);
    if (filters?.carrier_id) params.append('carrier_id', filters.carrier_id);
    if (filters?.search) params.append('search', filters.search);
    
    const response = await apiClient.get(`/api/documents?${params.toString()}`);
    return response.data;
  },

  // Get single document by ID
  async getDocument(documentId: string): Promise<PolicyDocumentWithText> {
    const response = await apiClient.get(`/api/documents/${documentId}`);
    return response.data;
  },

  // Upload new document
  async uploadDocument(file: File, carrierID?: string): Promise<PolicyDocument> {
    const formData = new FormData();
    formData.append('file', file);
    if (carrierID) {
      formData.append('carrier_id', carrierID);
    }

    const response = await apiClient.post('/api/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Delete document
  async deleteDocument(documentId: string): Promise<void> {
    await apiClient.delete(`/api/documents/${documentId}`);
  },

  // Bulk delete documents
  async bulkDeleteDocuments(documentIds: string[]): Promise<void> {
    // Since there's no bulk delete endpoint, delete one by one
    await Promise.all(documentIds.map(id => this.deleteDocument(id)));
  },

  // Get document processing status (for polling)
  async getDocumentStatus(documentId: string): Promise<{ processing_status: string; processing_error?: string }> {
    const response = await apiClient.get(`/api/documents/${documentId}`);
    return {
      processing_status: response.data.processing_status,
      processing_error: response.data.processing_error
    };
  },

  // Download document (if backend supports it)
  async downloadDocument(documentId: string): Promise<Blob> {
    const response = await apiClient.get(`/api/documents/${documentId}/download`, {
      responseType: 'blob'
    });
    return response.data;
  }
};

// Carrier API
export const carrierApi = {
  // Get all carriers with optional filtering
  async getCarriers(filters?: CarrierFilters): Promise<InsuranceCarrier[]> {
    const params = new URLSearchParams();
    if (filters?.search) params.append('search', filters.search);
    if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
    if (filters?.skip) params.append('skip', filters.skip.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const response = await apiClient.get(`/api/carriers?${params.toString()}`);
    return response.data;
  },

  // Get single carrier by ID
  async getCarrier(carrierId: string): Promise<InsuranceCarrier> {
    const response = await apiClient.get(`/api/carriers/${carrierId}`);
    return response.data;
  },

  // Create new carrier (admin only)
  async createCarrier(carrier: InsuranceCarrierCreate): Promise<InsuranceCarrier> {
    const response = await apiClient.post('/api/carriers', carrier);
    return response.data;
  },

  // Update carrier (admin only)
  async updateCarrier(carrierId: string, carrier: InsuranceCarrierUpdate): Promise<InsuranceCarrier> {
    const response = await apiClient.put(`/api/carriers/${carrierId}`, carrier);
    return response.data;
  },

  // Delete carrier (admin only)
  async deleteCarrier(carrierId: string): Promise<void> {
    await apiClient.delete(`/api/carriers/${carrierId}`);
  },

  // Get carrier statistics
  async getCarrierStats(carrierId: string): Promise<CarrierStats> {
    // Since there's no specific stats endpoint, we'll aggregate data
    const [policies, documents] = await Promise.all([
      policyApi.getPolicies({ carrier_id: carrierId }),
      documentApi.getDocuments({ carrier_id: carrierId })
    ]);

    return {
      total_policies: policies.length,
      total_documents: documents.length,
      active_policies: policies.filter(p => p.effective_date && new Date(p.effective_date) <= new Date()).length,
      recent_activity: Math.max(policies.length, documents.length) // Simple activity metric
    };
  }
};

// Dashboard API
export const dashboardApi = {
  // Get dashboard statistics
  async getDashboardStats(): Promise<DashboardStats> {
    // Since there's no specific dashboard endpoint, we'll aggregate data from multiple endpoints
    const [policies, documents, carriers] = await Promise.all([
      policyApi.getPolicies(),
      documentApi.getDocuments(),
      carrierApi.getCarriers()
    ]);

    // Calculate statistics
    const policiesByType = policies.reduce((acc, policy) => {
      const type = policy.policy_type || 'unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const policiesByCarrier = policies.reduce((acc, policy) => {
      if (policy.carrier_id) {
        const carrier = carriers.find(c => c.id === policy.carrier_id);
        const carrierName = carrier?.name || 'Unknown';
        acc[carrierName] = (acc[carrierName] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    // Generate recent activity from policies and documents
    const recentActivity = [
      ...policies.slice(0, 3).map(policy => ({
        id: policy.id,
        type: 'policy_created' as const,
        title: `Policy Created: ${policy.policy_name}`,
        description: `${policy.policy_type || 'Insurance'} policy was created`,
        timestamp: policy.created_at,
        policy_id: policy.id
      })),
      ...documents.slice(0, 3).map(doc => ({
        id: doc.id,
        type: 'document_uploaded' as const,
        title: `Document Uploaded: ${doc.original_filename}`,
        description: `Document processing status: ${doc.processing_status}`,
        timestamp: doc.created_at,
        document_id: doc.id
      }))
    ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 5);

    return {
      total_policies: policies.length,
      total_documents: documents.length,
      policies_by_type: policiesByType,
      policies_by_carrier: policiesByCarrier,
      recent_activity: recentActivity,
      red_flags_summary: {
        total: 0, // Will be populated when we fetch red flags
        by_severity: {}
      }
    };
  }
};

export default apiClient;
